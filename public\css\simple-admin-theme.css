/* ========================================
   Simple Admin Theme - ธีมเรียบง่าย สบายตา
   สำหรับระบบหลังบ้าน ศูนย์พัฒนาเด็กเล็ก
   ======================================== */

/* ========== Global Overrides ========== */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Prompt', 'Sarabun', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: #f8f9fa;
    color: #212529;
    line-height: 1.5;
}

/* ========== Layout ========== */
.main-content {
    background: #ffffff;
    min-height: 100vh;
    padding: 1.5rem;
}

.container-fluid {
    max-width: 1200px;
    margin: 0 auto;
}

/* ========== Cards - Simple Style ========== */
.card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    margin-bottom: 1.5rem;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.25rem;
    font-weight: 600;
    color: #495057;
}

.card-body {
    padding: 1.25rem;
}

.card-title {
    color: #212529;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

/* ========== Stats Cards ========== */
.stats-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.stats-card h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #495057;
}

.stats-card p {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.875rem;
}

.stats-card.news-card {
    border-left: 4px solid #17a2b8;
}

.stats-card.staff-card {
    border-left: 4px solid #28a745;
}

.stats-card.category-card {
    border-left: 4px solid #ffc107;
}

.stats-card.image-card {
    border-left: 4px solid #6c757d;
}

/* ========== Buttons - Simple Style ========== */
.btn {
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.875rem;
    border: 1px solid transparent;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
}

.btn-primary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
}

.btn-secondary {
    background-color: #495057;
    border-color: #495057;
    color: #ffffff;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #ffffff;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: #ffffff;
}

.btn-outline-primary {
    background-color: transparent;
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary {
    background-color: transparent;
    border-color: #495057;
    color: #495057;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* ========== Tables - Simple Style ========== */
.table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #e9ecef;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* ========== Forms - Simple Style ========== */
.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #495057;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 6px;
}

.form-control:focus {
    border-color: #6c757d;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #212529;
    font-size: 0.875rem;
}

.form-group {
    margin-bottom: 1rem;
}

/* ========== Badges - Simple Style ========== */
.badge {
    display: inline-block;
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 4px;
}

.badge-success {
    background-color: #28a745;
    color: #ffffff;
}

.badge-danger {
    background-color: #dc3545;
    color: #ffffff;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-info {
    background-color: #17a2b8;
    color: #ffffff;
}

.badge-secondary {
    background-color: #6c757d;
    color: #ffffff;
}

/* ========== Alerts - Simple Style ========== */
.alert {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 6px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* ========== Utilities ========== */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* ========== Responsive ========== */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .stats-card h2 {
        font-size: 1.5rem;
    }
    
    .btn {
        font-size: 0.8125rem;
        padding: 0.375rem 0.75rem;
    }
}
