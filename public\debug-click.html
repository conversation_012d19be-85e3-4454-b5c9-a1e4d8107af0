<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug การคลิก</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .test-element {
            margin: 10px;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 5px;
            cursor: pointer;
            background: #e7f3ff;
        }
        .test-element:hover {
            background: #cce7ff;
        }
        .overlay-test {
            position: relative;
            width: 200px;
            height: 100px;
            background: #28a745;
            margin: 20px;
        }
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .overlay.no-pointer {
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Debug การคลิก</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>ทดสอบ Element พื้นฐาน</h3>
                
                <div class="test-element" onclick="logClick('div-basic')">
                    คลิก DIV นี้
                </div>
                
                <button class="btn btn-primary" onclick="logClick('button-basic')">
                    คลิกปุ่มนี้
                </button>
                
                <a href="#" class="btn btn-success" onclick="logClick('link-basic'); return false;">
                    คลิกลิงก์นี้
                </a>
            </div>
            
            <div class="col-md-6">
                <h3>ทดสอบ Overlay</h3>
                
                <div class="overlay-test" onclick="logClick('overlay-parent')">
                    <div class="overlay" onclick="logClick('overlay-child')">
                        Overlay มี pointer-events
                    </div>
                </div>
                
                <div class="overlay-test" onclick="logClick('no-pointer-parent')">
                    <div class="overlay no-pointer" onclick="logClick('no-pointer-child')">
                        Overlay ไม่มี pointer-events
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>ข้อมูล Debug</h3>
                <div id="debug-log" class="debug-info">
                    <strong>Log การคลิก:</strong><br>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>ตรวจสอบ CSS</h3>
                <button class="btn btn-info" onclick="checkCSS()">ตรวจสอบ CSS ที่อาจมีปัญหา</button>
                <div id="css-info" class="debug-info" style="display: none;">
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>ตรวจสอบ JavaScript</h3>
                <button class="btn btn-warning" onclick="checkJS()">ตรวจสอบ JavaScript</button>
                <div id="js-info" class="debug-info" style="display: none;">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let clickCount = 0;
        
        function logClick(element) {
            clickCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            logDiv.innerHTML += `${clickCount}. [${timestamp}] คลิก: ${element}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(`Click detected: ${element} at ${timestamp}`);
        }
        
        function checkCSS() {
            const cssInfo = document.getElementById('css-info');
            cssInfo.style.display = 'block';
            
            let info = '<strong>ตรวจสอบ CSS:</strong><br>';
            
            // ตรวจสอบ stylesheets
            const stylesheets = document.styleSheets;
            info += `จำนวน Stylesheets: ${stylesheets.length}<br>`;
            
            for (let i = 0; i < stylesheets.length; i++) {
                const sheet = stylesheets[i];
                info += `${i + 1}. ${sheet.href || 'Inline styles'}<br>`;
            }
            
            // ตรวจสอบ computed styles ของ body
            const bodyStyles = window.getComputedStyle(document.body);
            info += `<br><strong>Body styles:</strong><br>`;
            info += `pointer-events: ${bodyStyles.pointerEvents}<br>`;
            info += `position: ${bodyStyles.position}<br>`;
            info += `z-index: ${bodyStyles.zIndex}<br>`;
            
            cssInfo.innerHTML = info;
        }
        
        function checkJS() {
            const jsInfo = document.getElementById('js-info');
            jsInfo.style.display = 'block';
            
            let info = '<strong>ตรวจสอบ JavaScript:</strong><br>';
            
            // ตรวจสอบ global objects
            info += `jQuery: ${typeof $ !== 'undefined' ? 'โหลดแล้ว' : 'ไม่พบ'}<br>`;
            info += `Bootstrap: ${typeof bootstrap !== 'undefined' ? 'โหลดแล้ว' : 'ไม่พบ'}<br>`;
            info += `ForceNormalColors: ${typeof window.ForceNormalColors !== 'undefined' ? 'โหลดแล้ว' : 'ไม่พบ'}<br>`;
            
            // ตรวจสอบ event listeners
            info += `<br><strong>Event Listeners:</strong><br>`;
            const testButton = document.querySelector('.btn-primary');
            if (testButton) {
                info += `ปุ่มทดสอบมี onclick: ${testButton.onclick ? 'ใช่' : 'ไม่'}<br>`;
            }
            
            // ตรวจสอบ errors
            info += `<br><strong>Console Errors:</strong><br>`;
            info += 'ตรวจสอบ Console ของเบราว์เซอร์สำหรับ errors<br>';
            
            jsInfo.innerHTML = info;
        }
        
        // ตรวจสอบการโหลดหน้า
        document.addEventListener('DOMContentLoaded', function() {
            logClick('DOMContentLoaded');
        });
        
        window.addEventListener('load', function() {
            logClick('window.load');
        });
        
        // ตรวจสอบ click events ทั่วไป
        document.addEventListener('click', function(e) {
            console.log('Global click detected:', e.target);
        });
        
        // ตรวจสอบ errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            logClick(`ERROR: ${e.error.message}`);
        });
    </script>
</body>
</html>
