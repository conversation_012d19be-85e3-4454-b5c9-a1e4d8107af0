<?php
echo "=== การตรวจสอบการตั้งค่า PHP สำหรับการอัปโหลดไฟล์ ===\n\n";

// ตรวจสอบการตั้งค่าที่สำคัญ
$settings = [
    'file_uploads' => 'การอัปโหลดไฟล์',
    'upload_max_filesize' => 'ขนาดไฟล์สูงสุดต่อไฟล์',
    'post_max_size' => 'ขนาด POST สูงสุด',
    'max_file_uploads' => 'จำนวนไฟล์สูงสุดที่อัปโหลดได้',
    'max_execution_time' => 'เวลาประมวลผลสูงสุด (วินาที)',
    'max_input_time' => 'เวลารับข้อมูลสูงสุด (วินาที)',
    'memory_limit' => 'ขีดจำกัดหน่วยความจำ'
];

foreach ($settings as $setting => $description) {
    $value = ini_get($setting);
    echo sprintf("%-20s: %-15s (%s)\n", $setting, $value, $description);
}

echo "\n=== คำแนะนำสำหรับการอัปโหลดไฟล์ไม่จำกัด ===\n";
echo "1. max_file_uploads: ปัจจุบัน " . ini_get('max_file_uploads') . " ไฟล์\n";
echo "   - แนะนำให้เพิ่มเป็น 200 หรือมากกว่า\n";
echo "   - แก้ไขใน php.ini: max_file_uploads = 200\n\n";

echo "2. post_max_size: ปัจจุบัน " . ini_get('post_max_size') . "\n";
echo "   - ต้องมากกว่า upload_max_filesize * max_file_uploads\n";
echo "   - แนะนำ: post_max_size = 1G\n\n";

echo "3. upload_max_filesize: ปัจจุบัน " . ini_get('upload_max_filesize') . "\n";
echo "   - ขนาดไฟล์เดี่ยวสูงสุด\n";
echo "   - แนะนำ: upload_max_filesize = 10M\n\n";

echo "4. max_execution_time: ปัจจุบัน " . ini_get('max_execution_time') . " วินาที\n";
echo "   - เวลาประมวลผลสำหรับอัปโหลดไฟล์จำนวนมาก\n";
echo "   - แนะนำ: max_execution_time = 300\n\n";

echo "5. memory_limit: ปัจจุบัน " . ini_get('memory_limit') . "\n";
echo "   - หน่วยความจำสำหรับประมวลผลรูปภาพ\n";
echo "   - แนะนำ: memory_limit = 512M\n\n";

// ตรวจสอบ GD Extension
echo "=== การตรวจสอบ GD Extension ===\n";
if (extension_loaded('gd')) {
    echo "✓ GD Extension: เปิดใช้งานแล้ว\n";
    $gdInfo = gd_info();
    echo "  - เวอร์ชัน: " . $gdInfo['GD Version'] . "\n";
    echo "  - รองรับ JPEG: " . ($gdInfo['JPEG Support'] ? 'ใช่' : 'ไม่') . "\n";
    echo "  - รองรับ PNG: " . ($gdInfo['PNG Support'] ? 'ใช่' : 'ไม่') . "\n";
    echo "  - รองรับ GIF: " . ($gdInfo['GIF Read Support'] ? 'ใช่' : 'ไม่') . "\n";
} else {
    echo "✗ GD Extension: ไม่ได้เปิดใช้งาน\n";
    echo "  - ต้องเปิดใช้งานใน php.ini: extension=gd\n";
}

echo "\n=== ไฟล์ php.ini ที่ใช้งาน ===\n";
echo "ตำแหน่ง: " . php_ini_loaded_file() . "\n";

echo "\n=== การแก้ไข php.ini ===\n";
echo "แก้ไขไฟล์ php.ini และเพิ่ม/แก้ไขค่าต่อไปนี้:\n\n";
echo "file_uploads = On\n";
echo "upload_max_filesize = 10M\n";
echo "post_max_size = 1G\n";
echo "max_file_uploads = 200\n";
echo "max_execution_time = 300\n";
echo "max_input_time = 300\n";
echo "memory_limit = 512M\n";
echo "extension=gd\n\n";
echo "หลังจากแก้ไขแล้ว ให้ restart Apache/Nginx\n";
