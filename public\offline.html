<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ไม่สามารถเชื่อมต่อได้ - ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Prompt', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        /* Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .offline-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 3rem 2.5rem;
            text-align: center;
            max-width: 520px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            color: #1e293b;
        }

        .offline-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .server-icon {
            width: 110px;
            height: 110px;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 12px 40px rgba(239, 68, 68, 0.25);
            animation: pulse 2s infinite;
        }

        .server-icon i {
            font-size: 2.75rem;
            color: white;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 12px 40px rgba(239, 68, 68, 0.25);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 16px 50px rgba(239, 68, 68, 0.35);
            }
        }

        .server-info {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 16px;
            padding: 1.75rem;
            margin-bottom: 2rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }

        .server-address {
            font-family: 'Courier New', monospace;
            font-size: 1.3rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.75rem;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .connection-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            font-size: 1rem;
            color: #ef4444;
            font-weight: 500;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border-radius: 50%;
            animation: blink 1.5s infinite;
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }

        @keyframes blink {
            0%, 50% { 
                opacity: 1; 
                transform: scale(1);
            }
            51%, 100% { 
                opacity: 0.3; 
                transform: scale(0.8);
            }
        }

        .error-title {
            font-size: 1.6rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .error-message {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 2.5rem;
            line-height: 1.6;
        }

        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-modern {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border: none;
            color: white;
            font-weight: 500;
            border-radius: 12px;
            padding: 14px 28px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
            font-size: 1rem;
            cursor: pointer;
        }

        .btn-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(59, 130, 246, 0.35);
            color: white;
            text-decoration: none;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #e2e8f0;
            color: #64748b;
            box-shadow: none;
        }

        .btn-outline:hover {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-color: #3b82f6;
            color: #1e293b;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .retry-info {
            margin-top: 2rem;
            padding: 1.25rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.05));
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .retry-info small {
            color: #64748b;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .offline-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
            }
            
            .btn-modern {
                width: 100%;
                justify-content: center;
            }
        }

        /* Animation for container entrance */
        .offline-container {
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="server-icon">
            <i class="fas fa-server"></i>
        </div>

        <div class="server-info">
            <div class="server-address">127.0.0.1:8000 ปิดการใช้งาน</div>
            <div class="connection-status">
                <div class="status-dot"></div>
                <span>คุณต้องการเข้าถึง "เว็บไซต์ของเรา (ไม่ปลอดภัย)" หรือไม่?</span>
            </div>
        </div>

        <h1 class="error-title">การเชื่อมต่อกับเซิร์ฟเวอร์ล้มเหลว</h1>

        <p class="error-message">
            ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้ในขณะนี้<br>
            กรุณาตรวจสอบการเชื่อมต่ออินเทอร์เน็ตของคุณหรือลองใหม่อีกครั้ง
        </p>

        <div class="error-actions">
            <button onclick="retryConnection()" class="btn-modern" id="retryBtn">
                <i class="fas fa-redo"></i>
                <span id="retryText">ลองใหม่</span>
            </button>
            <button onclick="goBack()" class="btn-modern btn-outline">
                <i class="fas fa-arrow-left"></i>
                ย้อนกลับ
            </button>
        </div>

        <div class="retry-info">
            <small>
                <i class="fas fa-info-circle"></i>
                ระบบจะพยายามเชื่อมต่อใหม่อัตโนมัติทุก 10 วินาที
            </small>
        </div>
    </div>

    <script>
        function retryConnection() {
            const retryBtn = document.getElementById('retryBtn');
            const retryText = document.getElementById('retryText');
            
            retryBtn.disabled = true;
            retryBtn.style.opacity = '0.7';
            retryText.innerHTML = '<span class="loading-spinner"></span> กำลังลอง...';
            
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        }

        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }

        // Auto retry logic
        let autoRetryCount = 0;
        const maxAutoRetries = 10;
        
        function autoRetry() {
            if (autoRetryCount < maxAutoRetries) {
                autoRetryCount++;
                
                fetch(window.location.href, { 
                    method: 'HEAD',
                    cache: 'no-cache'
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    }
                })
                .catch(() => {
                    // Continue auto retry
                });
            } else {
                document.querySelector('.retry-info small').innerHTML = 
                    '<i class="fas fa-exclamation-triangle"></i> หยุดการลองใหม่อัตโนมัติแล้ว - กรุณาลองใหม่ด้วยตนเอง';
            }
        }

        // Start auto retry after 5 seconds, then every 10 seconds
        setTimeout(() => {
            autoRetry();
            setInterval(autoRetry, 10000);
        }, 5000);

        // Handle online/offline events
        window.addEventListener('online', () => {
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        window.addEventListener('offline', () => {
            document.querySelector('.connection-status span').textContent = 'ไม่มีการเชื่อมต่ออินเทอร์เน็ต';
        });
    </script>
</body>
</html>
