@extends('layouts.admin')

@section('title', 'จัดการหมวดหมู่รูปภาพ - ระบบจัดการ')
@section('page-title', 'จัดการหมวดหมู่รูปภาพ')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">รายการหมวดหมู่รูปภาพ</h4>
    <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>เพิ่มหมวดหมู่
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($categories->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ชื่อหมวดหมู่</th>
                            <th>คำอธิบาย</th>
                            <th style="width: 120px;">จำนวนรูปภาพ</th>
                            <th style="width: 150px;">การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($categories as $category)
                        <tr>
                            <td>
                                <strong>{{ $category->name }}</strong>
                            </td>
                            <td>
                                @if($category->description)
                                    {{ Str::limit($category->description, 80) }}
                                @else
                                    <small class="text-muted">ไม่มีคำอธิบาย</small>
                                @endif
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ $category->images_count ?? 0 }}</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.categories.edit', $category->id) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.categories.destroy', $category->id) }}"
                                          method="POST"
                                          style="display: inline;"
                                          onsubmit="return confirm('คุณต้องการลบหมวดหมู่ {{ $category->name }} หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="btn btn-sm btn-outline-danger"
                                                title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $categories->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-tags fa-5x text-muted mb-4"></i>
                <h5 class="text-muted">ยังไม่มีหมวดหมู่รูปภาพ</h5>
                <p class="text-muted">เริ่มต้นสร้างหมวดหมู่แรกของคุณ</p>
                <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มหมวดหมู่
                </a>
            </div>
        @endif
    </div>
</div>


@endsection


