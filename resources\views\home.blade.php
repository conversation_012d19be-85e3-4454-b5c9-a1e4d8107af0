@extends('layouts.app')

@section('title', 'ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า')

@section('styles')
<style>
    .min-vh-75 {
        min-height: 75vh;
    }

    /* Hero <PERSON> Slider */
    .hero-banner {
        position: relative;
        height: 60vh;
        overflow: hidden;
        min-height: 500px;
        max-height: 700px;
    }

    .banner-slide {
        height: 60vh;
        min-height: 500px;
        max-height: 700px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        position: relative;
        display: flex;
        align-items: center;
    }

    .banner-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 100%);
        z-index: 1;
    }

    .banner-content {
        position: relative;
        z-index: 2;
    }

    .carousel-item {
        /* No transitions */
    }

    .carousel-fade .carousel-item {
        opacity: 0;
        transform: none;
    }

    .carousel-fade .carousel-item.active,
    .carousel-fade .carousel-item-next.carousel-item-start,
    .carousel-fade .carousel-item-prev.carousel-item-end {
        z-index: 1;
        opacity: 1;
    }

    .carousel-fade .active.carousel-item-start,
    .carousel-fade .active.carousel-item-end {
        z-index: 0;
        opacity: 0;
    }

    .carousel-indicators {
        bottom: 20px;
    }

    .carousel-indicators button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin: 0 5px;
        background-color: rgba(255, 255, 255, 0.5);
        border: 2px solid rgba(255, 255, 255, 0.8);
    }

    .carousel-indicators button.active {
        background-color: #ffc107;
        border-color: #ffc107;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 5%;
        opacity: 0.8;
    }

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
        width: 2.5rem;
        height: 2.5rem;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        padding: 0.5rem;
    }

    /* Carousel Indicators */
    .carousel-indicators {
        bottom: 2rem;
        margin-bottom: 0;
    }

    .carousel-indicators [data-bs-target] {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin: 0 5px;
        background-color: rgba(255, 255, 255, 0.5);
        border: 2px solid rgba(255, 255, 255, 0.8);
    }

    .carousel-indicators .active {
        background-color: #fff;
    }

    /* Animations removed for basic design */

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .hero-banner {
            height: 50vh;
            min-height: 400px;
            max-height: 500px;
        }

        .banner-slide {
            height: 50vh;
            min-height: 400px;
            max-height: 500px;
        }

        .banner-content h1 {
            font-size: 2.5rem !important;
        }

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
            width: 2rem;
            height: 2rem;
        }

        .carousel-indicators button {
            width: 10px;
            height: 10px;
        }
    }

    @media (max-width: 576px) {
        .hero-banner {
            height: 45vh;
            min-height: 350px;
            max-height: 450px;
        }

        .banner-slide {
            height: 45vh;
            min-height: 350px;
            max-height: 450px;
        }

        .banner-content h1 {
            font-size: 2rem !important;
        }
    }

    .btn {
        /* No hover effects */
    }

    /* Service Cards */
    .service-card {
        border-radius: 15px !important;
    }

    /* Staff Cards */
    .staff-card {
        background: transparent;
        border: none;
        text-align: center;
        padding: 1rem;
        margin-bottom: 2rem;
        transition: transform 0.3s ease;
    }

    .staff-card:hover {
        transform: translateY(-5px);
    }

    .staff-card:hover .staff-avatar,
    .staff-card:hover .staff-avatar-placeholder {
        transform: scale(1.05);
    }

    .staff-avatar {
        width: 200px;
        height: 200px;
        object-fit: cover;
        object-position: center;
        border: 4px solid #28a745;
        border-radius: 50%;
        margin: 0 auto;
        display: block;
        transition: transform 0.3s ease;
    }

    .staff-avatar img,
    .staff-avatar-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        border-radius: 50%;
    }



    .staff-name {
        font-size: 1.2rem;
        font-weight: 600;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .staff-position {
        font-size: 1rem;
        color: #6c757d;
        margin-bottom: 0;
    }

    .staff-avatar-placeholder {
        width: 200px;
        height: 200px;
        background: #f8f9fa;
        border: 4px solid #28a745;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        color: #6c757d;
        transition: transform 0.3s ease;
    }

    .staff-avatar-placeholder i {
        font-size: 3rem;
    }








    .staff-section {
        background: #ffffff;
        border-top: 1px solid #e9ecef;
        border-bottom: 1px solid #e9ecef;
    }



    .service-icon {
        position: relative;
    }

    .icon-wrapper {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        position: relative;
        overflow: hidden;
    }

    .icon-wrapper::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
        border-radius: 50%;
    }

    .icon-wrapper i {
        font-size: 2rem;
        color: white;
        z-index: 1;
    }

    /* Animations removed for basic design */

    /* Responsive adjustments for staff section */
    @media (max-width: 768px) {
        .staff-avatar,
        .staff-avatar-placeholder {
            width: 170px;
            height: 170px;
        }

        .staff-avatar-placeholder i {
            font-size: 3rem;
        }

        .staff-name {
            font-size: 1.1rem;
        }

        .staff-position {
            font-size: 0.9rem;
        }
    }

    @media (max-width: 576px) {
        .staff-avatar,
        .staff-avatar-placeholder {
            width: 150px;
            height: 150px;
        }

        .staff-avatar-placeholder i {
            font-size: 2.5rem;
        }

        .staff-name {
            font-size: 1rem;
        }

        .staff-position {
            font-size: 0.85rem;
        }
    }

    /* Contact Slide Section Styles */
    .contact-slide-section {
        position: relative;
        overflow: hidden;
        min-height: 400px;
    }

    .contact-slide-container {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .contact-slide-background {
        background: #4a90e2;  /* Soft Blue - เหมาะสำหรับเด็ก */
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .colorful-hands {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
    }

    .hand {
        position: absolute;
        width: 80px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        opacity: 0.3;
    }

    .hand-1 {
        top: 20%;
        left: 10%;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,80 Q30,20 50,30 Q70,40 80,80 Q60,90 40,85 Q20,90 20,80 Z" fill="%23ffffff" opacity="0.3"/></svg>');
    }

    .hand-2 {
        top: 15%;
        right: 15%;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,80 Q30,20 50,30 Q70,40 80,80 Q60,90 40,85 Q20,90 20,80 Z" fill="%23ffffff" opacity="0.25"/></svg>');
    }

    .hand-3 {
        bottom: 25%;
        left: 20%;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,80 Q30,20 50,30 Q70,40 80,80 Q60,90 40,85 Q20,90 20,80 Z" fill="%23ffffff" opacity="0.35"/></svg>');
    }

    .hand-4 {
        top: 40%;
        right: 25%;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,80 Q30,20 50,30 Q70,40 80,80 Q60,90 40,85 Q20,90 20,80 Z" fill="%23ffffff" opacity="0.2"/></svg>');
    }

    .hand-5 {
        bottom: 15%;
        right: 10%;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,80 Q30,20 50,30 Q70,40 80,80 Q60,90 40,85 Q20,90 20,80 Z" fill="%23ffffff" opacity="0.4"/></svg>');
    }

    .hand-6 {
        top: 60%;
        left: 15%;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,80 Q30,20 50,30 Q70,40 80,80 Q60,90 40,85 Q20,90 20,80 Z" fill="%23ffffff" opacity="0.3"/></svg>');
    }

    .hand-7 {
        bottom: 40%;
        right: 20%;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,80 Q30,20 50,30 Q70,40 80,80 Q60,90 40,85 Q20,90 20,80 Z" fill="%23ffffff" opacity="0.25"/></svg>');
    }

    .contact-slide-content {
        position: relative;
        z-index: 3;
        padding: 4rem 0;
        color: white;
    }

    .contact-slide-text {
        text-align: center;
    }

    .contact-slide-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        line-height: 1.2;
    }

    .contact-slide-subtitle {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.95;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .contact-quick-info {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }

    .contact-info-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255,255,255,0.25);
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255,255,255,0.4);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .contact-info-item i {
        font-size: 1.1rem;
    }

    /* Contact Details Styles */
    .contact-details {
        margin-top: 2rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: left;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        height: 100%;
    }

    .contact-icon {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .contact-icon i {
        font-size: 1.5rem;
        color: #fff;
    }

    .contact-text h5 {
        color: #fff;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .contact-text p {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 0.95rem;
        line-height: 1.4;
    }



    /* Responsive Design for Contact Slide */
    @media (max-width: 768px) {
        .contact-slide-title {
            font-size: 2rem;
        }

        .contact-slide-subtitle {
            font-size: 1rem;
        }

        .contact-quick-info {
            gap: 1rem;
        }

        .contact-item {
            padding: 1rem;
            text-align: center;
            flex-direction: column;
        }

        .contact-icon {
            margin-right: 0;
            margin-bottom: 0.75rem;
            width: 50px;
            height: 50px;
        }

        .contact-icon i {
            font-size: 1.2rem;
        }

        .contact-text h5 {
            font-size: 1rem;
        }

        .contact-text p {
            font-size: 0.9rem;
        }

        .contact-info-item {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .hand {
            width: 60px;
            height: 60px;
        }
    }

    @media (max-width: 576px) {
        .contact-slide-content {
            padding: 3rem 0;
        }

        .contact-slide-title {
            font-size: 1.75rem;
        }

        .contact-quick-info {
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .contact-details .row {
            flex-direction: column;
        }

        .contact-item {
            padding: 0.75rem;
            margin-bottom: 1rem;
        }

        .contact-icon {
            width: 45px;
            height: 45px;
        }

        .contact-icon i {
            font-size: 1.1rem;
        }

        .contact-text h5 {
            font-size: 0.95rem;
        }

        .contact-text p {
            font-size: 0.85rem;
        }

</style>
@endsection

@section('content')
<!-- Hero Banner Carousel -->
<section class="hero-banner">
    <div id="heroCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="5000">
        <div class="carousel-indicators">
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
        </div>

        <div class="carousel-inner">
            <!-- Slide 1 -->
            <div class="carousel-item active">
                <div class="banner-slide" style="background-image: url('https://scontent.fbkk5-3.fna.fbcdn.net/v/t39.30808-6/479988701_2111513645929522_6860530791663967266_n.jpg?_nc_cat=111&ccb=1-7&_nc_sid=cc71e4&_nc_ohc=MEdr4rGTXasQ7kNvwGNO8H8&_nc_oc=AdmJLRadgJklGkUHHvPoiGpELHy7WpNCjDqOR7BNyb0jUgSkiBeICBTFOStTC4fUffSKD0tO6KyfkdbdWOkv8SqH&_nc_zt=23&_nc_ht=scontent.fbkk5-3.fna&_nc_gid=NQcSrSfFNVD9092c-M_3RQ&oh=00_AfRknr2QrQ7HOsxqIIbez6wnDh7pMk_wArJqONAJ1Z4uKQ&oe=68810AA8');">
                    <div class="banner-overlay"></div>
                    <div class="container">
                        <div class="row align-items-center min-vh-75">
                            <div class="col-lg-8">
                                <div class="banner-content">
                                    <div class="badge bg-light text-dark mb-3 px-3 py-2">
                                        <i class="fas fa-star me-2"></i>ศูนย์พัฒนาเด็กเล็กคุณภาพ
                                    </div>
                                    <h1 class="display-4 fw-bold mb-4 text-white">
                                        ยินดีต้อนรับสู่<br>
                                        <span class="text-info">ศูนย์พัฒนาเด็กเล็ก</span><br>
                                        <span class="fs-3">ตำบลบุ่งคล้า</span>
                                    </h1>
                                    <p class="lead text-white-50 mb-4">
                                        ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า องค์การบริหารส่วนตำบลบุ่งคล้า อำเภอหล่มสัก จังหวัดเพชรบูรณ์
                                    </p>
                                    <div class="d-flex flex-wrap gap-3">
                                        <a href="{{ route('contact') }}" class="btn btn-info btn-lg px-4 py-3">
                                            <i class="fas fa-phone me-2"></i>ติดต่อเรา
                                        </a>
                                        <a href="{{ route('news.index') }}" class="btn btn-outline-light btn-lg px-4 py-3">
                                            <i class="fas fa-newspaper me-2"></i>ข่าวสาร
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="carousel-item">
                <div class="banner-slide" style="background-image: url('https://scontent.fbkk5-1.fna.fbcdn.net/v/t1.6435-9/90166208_889377384809827_5974870619127283712_n.jpg?_nc_cat=109&ccb=1-7&_nc_sid=86c6b0&_nc_ohc=xYkDylRaN8cQ7kNvwFqhbOH&_nc_oc=AdnWTtqI17_PpShOTQWVQaV-EJCifP1PBL6uIPkdtyRe_qamQlnMiQEH3u-CgiyOabaOdkf400bHSLg9MLeckEea&_nc_zt=23&_nc_ht=scontent.fbkk5-1.fna&_nc_gid=RrB_gsZCRhZRUQPXL30Dlg&oh=00_AfQmXBl00vcOnX8V3yw2nJzPVQl4NWDBxU1ceGBWjcye3A&oe=68A335FF');">
                    <div class="banner-overlay"></div>
                    <div class="container">
                        <div class="row align-items-center min-vh-75">
                            <div class="col-lg-8">
                                <div class="banner-content">
                                    <div class="badge bg-light text-dark mb-3 px-3 py-2">
                                        <i class="fas fa-heart me-2"></i>พัฒนาการเด็กเล็ก
                                    </div>
                                    <h1 class="display-4 fw-bold mb-4 text-white">
                                        พัฒนาเด็กเล็ก<br>
                                        <span class="text-warning">ด้วยความรักและใส่ใจ</span><br>
                                        <span class="fs-3">เพื่ออนาคตที่ดี</span>
                                    </h1>
                                    <p class="lead text-white-50 mb-4">
                                        ส่งเสริมพัฒนาการเด็กวัย 2-5 ปี ด้วยหลักสูตรที่เหมาะสมและกิจกรรมที่หลากหลาย
                                    </p>
                                    <div class="d-flex flex-wrap gap-3">
                                        <a href="{{ route('staff.index') }}" class="btn btn-warning btn-lg px-4 py-3">
                                            <i class="fas fa-users me-2"></i>ทีมงาน
                                        </a>
                                        <a href="{{ route('gallery.index') }}" class="btn btn-outline-light btn-lg px-4 py-3">
                                            <i class="fas fa-images me-2"></i>รูปภาพกิจกรรม
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
            <span class="visually-hidden">Previous</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
            <span class="visually-hidden">Next</span>
        </button>
    </div>
</section>






<!-- Services Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <div class="section-header">
                    <span class="badge bg-primary mb-3 px-3 py-2">
                        <i class="fas fa-star me-2"></i>บริการของเรา
                    </span>
                    <h2 class="fw-bold text-dark mb-3">พัฒนาเด็กเล็กอย่างครบถ้วน</h2>
                    <p class="lead text-muted">เราให้บริการที่หลากหลายเพื่อพัฒนาเด็กเล็กในทุกมิติ</p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="service-card card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="service-icon mb-4">
                            <div class="icon-wrapper bg-primary">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                        </div>
                        <h5 class="card-title fw-bold mb-3">การเรียนการสอน</h5>
                        <p class="card-text text-muted">
                            หลักสูตรการเรียนรู้ที่เหมาะสมกับวัยและพัฒนาการของเด็กเล็ก
                            พร้อมกิจกรรมที่ส่งเสริมความคิดสร้างสรรค์
                        </p>
                        <div class="service-features">
                            <small class="text-primary">
                                <i class="fas fa-check me-2"></i>หลักสูตรมาตรฐาน
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="service-card card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="service-icon mb-4">
                            <div class="icon-wrapper bg-success">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                        </div>
                        <h5 class="card-title fw-bold mb-3">ดูแลสุขภาพ</h5>
                        <p class="card-text text-muted">
                            การดูแลสุขภาพและโภชนาการที่เหมาะสมสำหรับเด็กเล็ก
                            พร้อมติดตามพัฒนาการอย่างใกล้ชิด
                        </p>
                        <div class="service-features">
                            <small class="text-success">
                                <i class="fas fa-check me-2"></i>โภชนาการครบถ้วน
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="service-card card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="service-icon mb-4">
                            <div class="icon-wrapper bg-warning">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <h5 class="card-title fw-bold mb-3">กิจกรรมสังคม</h5>
                        <p class="card-text text-muted">
                            กิจกรรมที่ส่งเสริมการเรียนรู้ทางสังคมและการทำงานร่วมกัน
                            เพื่อพัฒนาทักษะชีวิต
                        </p>
                        <div class="service-features">
                            <small class="text-warning">
                                <i class="fas fa-check me-2"></i>ทักษะสังคม
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Staff Section -->
<section class="py-5 staff-section">
    <div class="container">
        @if($staffMembers->count() > 0)
            @php
                // จัดเรียงบุคลากรตามลำดับความสำคัญใหม่ (ครูชำนาญการอันดับแรก)
                $sortedStaff = $staffMembers->sortBy(function($member) {
                    $position = strtolower($member->position);
                    if (str_contains($position, 'ครูชำนาญการ')) {
                        return 1; // ครูชำนาญการอันดับ 1
                    } elseif (str_contains($position, 'ผู้อำนวยการ') || str_contains($position, 'อำนวยการ')) {
                        return 2; // ผู้อำนวยการอันดับ 2
                    } elseif (str_contains($position, 'รอง')) {
                        return 3; // รองผู้อำนวยการอันดับ 3
                    } elseif (str_contains($position, 'ครูประจำ') || str_contains($position, 'ครูผู้ดูแล')) {
                        return 4; // ครูประจำอันดับ 4
                    } elseif (str_contains($position, 'พยาบาล')) {
                        return 5; // พยาบาลอันดับ 5
                    } elseif (str_contains($position, 'ครูผู้ช่วย') || str_contains($position, 'ผู้ช่วยครู')) {
                        return 6; // ครูผู้ช่วยอันดับ 6
                    } else {
                        return 7; // ตำแหน่งอื่นๆ อันดับ 7
                    }
                });

                // แยกครูชำนาญการออกมา
                $expertTeachers = $sortedStaff->filter(function($member) {
                    $position = strtolower($member->position);
                    return str_contains($position, 'ครูชำนาญการ');
                });

                // ทีมงานสนับสนุนอื่นๆ
                $supportStaff = $sortedStaff->filter(function($member) {
                    $position = strtolower($member->position);
                    return !str_contains($position, 'ครูชำนาญการ');
                });
            @endphp

            <!-- ครูชำนาญการ - แสดงแยกต่างหากด้านบน -->
            @if($expertTeachers->count() > 0)
                <div class="row text-center mb-4">
                    <div class="col-12">
                        <div class="d-inline-block bg-success text-white px-4 py-2 rounded-pill mb-3">
                            <i class="fas fa-star me-2"></i>ครูชำนาญการ
                        </div>
                        <p class="text-muted">ผู้เชี่ยวชาญด้านการศึกษาปฐมวัย</p>
                    </div>
                </div>

                <div class="row justify-content-center mb-5">
                    @foreach($expertTeachers as $member)
                    <div class="col-lg-4 col-md-6 col-sm-8 mb-4">
                        <div class="staff-card" style="border: 3px solid #28a745; border-radius: 15px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
                            @if($member->photo)
                                <img src="{{ asset('storage/' . $member->photo) }}"
                                     alt="{{ $member->name }}"
                                     class="staff-avatar"
                                     style="border: 4px solid #28a745;">
                            @else
                                <div class="staff-avatar-placeholder" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: 4px solid #28a745;">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                </div>
                            @endif
                            <h5 class="staff-name" style="color: #28a745; font-weight: 700;">{{ $member->name }}</h5>
                            <p class="staff-position" style="color: #20c997; font-weight: 600;">{{ $member->position }} ⭐</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            @endif

            <!-- ทีมงานสนับสนุน -->
            @if($supportStaff->count() > 0)
                <div class="row text-center mb-4">
                    <div class="col-12">
                        <div class="section-header">
                            <span class="badge bg-primary mb-3 px-3 py-2">
                                <i class="fas fa-users me-2"></i>ทีมงานสนับสนุน
                            </span>
                            <p class="text-muted">บุคลากรที่ร่วมดูแลและพัฒนาเด็กเล็กในทุกด้าน</p>
                        </div>
                    </div>
                </div>

                <!-- แถวที่ 1 - 3 คนแรก -->
                <div class="row justify-content-center mb-4">
                    @foreach($supportStaff->take(3) as $member)
                    <div class="col-lg-4 col-md-6 col-sm-6 mb-4">
                        <div class="staff-card">
                            @if($member->photo)
                                <img src="{{ asset('storage/' . $member->photo) }}"
                                     alt="{{ $member->name }}"
                                     class="staff-avatar">
                            @else
                                <div class="staff-avatar-placeholder">
                                    @php
                                        $iconClass = 'fa-user';
                                        $position = strtolower($member->position);

                                        if (str_contains($position, 'ผู้อำนวยการ')) {
                                            $iconClass = 'fa-user-tie';
                                        } elseif (str_contains($position, 'รองผู้อำนวยการ')) {
                                            $iconClass = 'fa-user-graduate';
                                        } elseif (str_contains($position, 'ครู') && !str_contains($position, 'ครัว')) {
                                            $iconClass = 'fa-chalkboard-teacher';
                                        } elseif (str_contains($position, 'พยาบาล')) {
                                            $iconClass = 'fa-user-nurse';
                                        } elseif (str_contains($position, 'ผู้ช่วย')) {
                                            $iconClass = 'fa-graduation-cap';
                                        } elseif (str_contains($position, 'ครัว') || str_contains($position, 'โภชนาการ')) {
                                            $iconClass = 'fa-utensils';
                                        } elseif (str_contains($position, 'แม่บ้าน') || str_contains($position, 'ความสะอาด')) {
                                            $iconClass = 'fa-broom';
                                        } elseif (str_contains($position, 'ธุรการ')) {
                                            $iconClass = 'fa-file-alt';
                                        }
                                    @endphp
                                    <i class="fas {{ $iconClass }}"></i>
                                </div>
                            @endif
                            <h5 class="staff-name">{{ $member->name }}</h5>
                            <p class="staff-position">{{ $member->position }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- แถวที่ 2 - 3 คนถัดไป (ถ้ามี) -->
                @if($supportStaff->count() > 3)
                <div class="row justify-content-center">
                    @foreach($supportStaff->skip(3)->take(3) as $member)
                    <div class="col-lg-4 col-md-6 col-sm-6 mb-4">
                        <div class="staff-card">
                            @if($member->photo)
                                <img src="{{ asset('storage/' . $member->photo) }}"
                                     alt="{{ $member->name }}"
                                     class="staff-avatar">
                            @else
                                <div class="staff-avatar-placeholder">
                                    @php
                                        $iconClass = 'fa-user';
                                        $position = strtolower($member->position);

                                        if (str_contains($position, 'ผู้อำนวยการ')) {
                                            $iconClass = 'fa-user-tie';
                                        } elseif (str_contains($position, 'รองผู้อำนวยการ')) {
                                            $iconClass = 'fa-user-graduate';
                                        } elseif (str_contains($position, 'ครู') && !str_contains($position, 'ครัว')) {
                                            $iconClass = 'fa-chalkboard-teacher';
                                        } elseif (str_contains($position, 'พยาบาล')) {
                                            $iconClass = 'fa-user-nurse';
                                        } elseif (str_contains($position, 'ผู้ช่วย')) {
                                            $iconClass = 'fa-graduation-cap';
                                        } elseif (str_contains($position, 'ครัว') || str_contains($position, 'โภชนาการ')) {
                                            $iconClass = 'fa-utensils';
                                        } elseif (str_contains($position, 'แม่บ้าน') || str_contains($position, 'ความสะอาด')) {
                                            $iconClass = 'fa-broom';
                                        } elseif (str_contains($position, 'ธุรการ')) {
                                            $iconClass = 'fa-file-alt';
                                        }
                                    @endphp
                                    <i class="fas {{ $iconClass }}"></i>
                                </div>
                            @endif
                            <h5 class="staff-name">{{ $member->name }}</h5>
                            <p class="staff-position">{{ $member->position }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
                @endif
            @endif
        @else
            <div class="row text-center mb-5">
                <div class="col-12">
                    <div class="section-header">
                        <span class="badge bg-primary mb-3 px-3 py-2">
                            <i class="fas fa-users me-2"></i>ทีมงานของเรา
                        </span>
                        <h2 class="fw-bold text-dark mb-3">บุคลากรภายในศูนย์พัฒนาเด็กเล็ก</h2>
                        <p class="lead text-muted">ทีมงานมืออาชีพที่พร้อมดูแลและพัฒนาเด็กน้อยด้วยความรักและใส่ใจ</p>
                    </div>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-6 text-center">
                    <div class="staff-card">
                        <div class="staff-avatar-placeholder">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="staff-name">ยังไม่มีข้อมูลบุคลากร</h5>
                        <p class="staff-position">กรุณาติดตามข้อมูลในภายหลัง</p>
                    </div>
                </div>
            </div>
        @endif

        @if($staffMembers->count() > 0)
        <div class="text-center mt-5">
            <a href="{{ route('staff.index') }}" class="btn btn-outline-primary btn-lg">
                ดูบุคลากรทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
        @endif
    </div>
</section>

<!-- Latest News Section -->
@if($latestNews->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold text-primary">ข่าวสารล่าสุด</h2>
                <p class="text-muted">ติดตามข่าวสารและกิจกรรมของศูนย์</p>
            </div>
        </div>
        
        <div class="row g-4">
            @foreach($latestNews as $news)
            <div class="col-md-4">
                <div class="card news-card h-100">
                    @php
                        $newsImageUrl = null;
                        if ($news->featuredImage) {
                            $newsImageUrl = $news->featuredImage->thumbnail_url ?: $news->featuredImage->image_url;
                        } elseif ($news->images && $news->images->count() > 0) {
                            $newsImageUrl = $news->images->first()->thumbnail_url ?: $news->images->first()->image_url;
                        }
                    @endphp

                    @if($newsImageUrl)
                        <img src="{{ $newsImageUrl }}"
                             class="card-img-top"
                             alt="{{ $news->title }}"
                             style="height: 200px; object-fit: cover; object-position: center center;">
                    @else
                        <img src="https://via.placeholder.com/400x200/e9ecef/6c757d?text=ไม่มีรูปภาพ"
                             class="card-img-top"
                             alt="ไม่มีรูปภาพ"
                             style="height: 200px; object-fit: cover; object-position: center center;">
                    @endif
                    
                    <div class="card-body">
                        <h5 class="card-title">{{ $news->title }}</h5>
                        <p class="card-text">{{ Str::limit(strip_tags($news->content), 100) }}</p>

                        @if($news->images && $news->images->count() > 0)
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-images me-1"></i>
                                    {{ $news->images->count() }} รูปภาพ
                                </small>
                            </div>
                        @endif
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="{{ route('news.show', $news->id) }}" class="btn btn-primary btn-sm">
                            อ่านต่อ <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ route('news.index') }}" class="btn btn-outline-primary">
                ดูข่าวสารทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
    </div>
</section>
@endif

<!-- Recent Images Section -->
@if($recentImages->count() > 0)
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold text-primary">รูปภาพกิจกรรมล่าสุด</h2>
                <p class="text-muted">ชมภาพบรรยากาศกิจกรรมต่างๆ ของศูนย์พัฒนาเด็กเล็ก</p>
            </div>
        </div>

        <div class="row g-4">
            @foreach($recentImages as $image)
            <div class="col-lg-4 col-md-6">
                <div class="card image-card h-100 border-0 shadow-sm">
                    <div class="position-relative overflow-hidden">
                        <img src="{{ $image->thumbnail_url ?? $image->image_url }}"
                             class="card-img-top"
                             alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                             style="height: 250px; object-fit: cover; object-position: center center;">
                    </div>

                    <div class="card-body">
                        <h6 class="card-title">รูปภาพกิจกรรม</h6>
                        @if($image->description)
                            <p class="card-text text-muted small">{{ Str::limit($image->description, 80) }}</p>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="text-center mt-4">
            <a href="{{ route('gallery.index') }}" class="btn btn-outline-primary">
                ดูรูปภาพทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
    </div>
</section>


@endif

<!-- Contact Section -->
<section class="contact-slide-section">
    <div class="contact-slide-container">
        <div class="contact-slide-background">
            <div class="colorful-hands">
                <div class="hand hand-1"></div>
                <div class="hand hand-2"></div>
                <div class="hand hand-3"></div>
                <div class="hand hand-4"></div>
                <div class="hand hand-5"></div>
                <div class="hand hand-6"></div>
                <div class="hand hand-7"></div>
            </div>
        </div>

        <div class="contact-slide-content">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-8 mx-auto text-center">
                        <div class="contact-slide-text">
                            <h1 class="contact-slide-title">
                                ติดต่อศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า
                            </h1>
                            <p class="contact-slide-subtitle">
                                พร้อมดูแลและพัฒนาเด็กเล็กด้วยความรักและใส่ใจ
                            </p>

                            <!-- Contact Information -->
                            <div class="contact-details mt-4">
                                <div class="row justify-content-center g-4">
                                    <div class="col-md-5">
                                        <div class="contact-item">
                                            <div class="contact-icon">
                                                <i class="fab fa-facebook-f"></i>
                                            </div>
                                            <div class="contact-text">
                                                <h5>Facebook</h5>
                                                <p>ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="contact-item">
                                            <div class="contact-icon">
                                                <i class="fas fa-map-marker-alt"></i>
                                            </div>
                                            <div class="contact-text">
                                                <h5>ที่อยู่</h5>
                                                <p>อำเภอหล่มสัก ตำบลบุ่งคล้า จังหวัดเพชรบูรณ์</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    </div>
</section>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize carousel with custom settings
    const heroCarousel = document.getElementById('heroCarousel');
    if (heroCarousel) {
        const carousel = new bootstrap.Carousel(heroCarousel, {
            interval: 5000,
            wrap: true,
            touch: true
        });

        // Pause carousel on hover
        heroCarousel.addEventListener('mouseenter', function() {
            carousel.pause();
        });

        heroCarousel.addEventListener('mouseleave', function() {
            carousel.cycle();
        });

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                carousel.prev();
            } else if (e.key === 'ArrowRight') {
                carousel.next();
            }
        });
    }
});
</script>
@endsection
