@extends('layouts.admin')

@section('title', 'จัดการรูปภาพ - ระบบจัดการ')
@section('page-title', 'จัดการรูปภาพกิจกรรม')

@section('content')
<!-- แสดงข้อความแจ้งเตือน -->
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    @if(session('show_continue_upload'))
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-check-circle me-2"></i>
                {{ session('success') }}
            </div>
            <div>
                <a href="{{ route('admin.images.create') }}" class="btn btn-success btn-sm me-2">
                    <i class="fas fa-plus me-1"></i>อัพโหลดต่อ (ชุดถัดไป)
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
        <div class="mt-2">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                คุณสามารถอัพโหลดรูปภาพเพิ่มเติมได้อีก 20 รูปต่อครั้ง
            </small>
        </div>
    @else
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    @endif
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif



<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">รายการรูปภาพกิจกรรม</h4>
    <div>
        <a href="{{ route('admin.images.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>อัพโหลดรูปภาพ
        </a>
    </div>
</div>

<!-- Category Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.images.index') }}">
            <div class="row align-items-end">
                <div class="col-md-8">
                    <label for="category_filter" class="form-label">กรองตามหมวดหมู่</label>
                    <select class="form-select" id="category_filter" name="category_id" onchange="this.form.submit()">
                        <option value="">-- ทุกหมวดหมู่ --</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}"
                                    {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }} ({{ $category->images->count() }} รูป)
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-4">
                    @if(request('category_id'))
                        <a href="{{ route('admin.images.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>ล้างตัวกรอง
                        </a>
                    @endif
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        @if($images->count() > 0)
            <div class="row g-4">
                @foreach($images as $image)
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="card h-100">
                        <div class="position-relative">
                            <img src="{{ $image->thumbnail_url ?? $image->image_url }}"
                                 alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                                 class="card-img-top"
                                 style="height: 200px; object-fit: cover; object-position: center center;">
                            
                            @if(!$image->is_published)
                                <span class="position-absolute top-0 start-0 badge bg-warning m-2">
                                    ร่าง
                                </span>
                            @endif
                        </div>
                        
                        <div class="card-body">
                            @if($image->description)
                                <p class="card-text small">{{ Str::limit($image->description, 50) }}</p>
                            @else
                                <p class="card-text small text-muted">ไม่มีคำอธิบาย</p>
                            @endif

                            @if($image->category)
                                <div class="mb-2">
                                    <span class="badge bg-primary">{{ $image->category->name }}</span>
                                </div>
                            @endif

                            <div class="small text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $image->created_at->format('d/m/Y') }}
                            </div>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ route('admin.images.edit', $image->id) }}"
                                   class="btn btn-sm btn-outline-primary"
                                   title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.images.destroy', $image->id) }}"
                                      method="POST"
                                      style="display: inline;"
                                      onsubmit="return confirm('คุณต้องการลบรูปภาพนี้หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            class="btn btn-sm btn-outline-danger"
                                            title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $images->links('custom.admin-pagination') }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-images fa-5x text-muted mb-4"></i>
                <h5 class="text-muted">ยังไม่มีรูปภาพกิจกรรม</h5>
                <p class="text-muted">เริ่มต้นอัพโหลดรูปภาพแรกของคุณ</p>
                <a href="{{ route('admin.images.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>อัพโหลดรูปภาพ
                </a>
            </div>
        @endif
    </div>
</div>


@endsection

@section('styles')
<style>
    /* ========== Toggle Switch Styling ========== */
    .form-check-input:checked {
        background-color: #00d084 !important;
        border-color: #00d084 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 208, 132, 0.25) !important;
    }

    .form-check-input:focus {
        border-color: #80bdff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    .form-check-input {
        width: 3.5rem !important;
        height: 1.8rem !important;
        border-radius: 1rem !important;
        background-color: #dee2e6 !important;
        border: 2px solid #dee2e6 !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
    }

    .form-check-input:checked::before {
        transform: translateX(1.7rem) !important;
    }

    .status-text {
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        margin-left: 0.5rem !important;
    }

    .form-check {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
    }
</style>
@endsection

@section('scripts')
@endsection
