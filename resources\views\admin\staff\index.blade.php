@extends('layouts.admin')

@section('title', 'จัดการบุคลากร - ระบบจัดการ')
@section('page-title', 'จัดการบุคลากร')

@section('styles')
<style>
    /* ========== ปรับสีฟอนต์ในตารางบุคลากรให้เป็นสีเดียวกัน ========== */
    .table th {
        background: linear-gradient(135deg, #e8f4fd, #d1ecf1) !important;
        color: #1a252f !important;
        font-weight: 700 !important;
        text-align: center !important;
        border: 2px solid #3498db !important;
        padding: 15px 10px !important;
        text-shadow: none !important;
        font-size: 0.95rem !important;
    }

    .table td {
        color: #1a252f !important;
        font-weight: 500 !important;
        vertical-align: middle !important;
        text-align: center !important;
        padding: 12px 10px !important;
        border-bottom: 1px solid #e9ecef !important;
    }

    .table td strong {
        color: #1a252f !important;
        font-weight: 600 !important;
    }

    .table td small {
        color: #1a252f !important;
        font-weight: 500 !important;
    }

    .table td .text-muted {
        color: #1a252f !important;
        font-weight: 500 !important;
    }

    /* Table row hover effects removed */

    /* ========== ปรับสี Badge ในตาราง ========== */
    .table .badge.bg-success {
        background-color: #28a745 !important;
        color: #ffffff !important;
        font-weight: 600 !important;
        padding: 8px 12px !important;
        border-radius: 20px !important;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3) !important;
        border: 2px solid #ffffff !important;
    }

    .table .badge.bg-secondary {
        background-color: #6c757d !important;
        color: #ffffff !important;
        font-weight: 600 !important;
        padding: 8px 12px !important;
        border-radius: 20px !important;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3) !important;
        border: 2px solid #ffffff !important;
    }

    /* ========== ปรับสีไอคอนในตาราง ========== */
    .table .fa-user {
        color: #3498db !important;
    }

    .table .fa-phone,
    .table .fa-envelope {
        color: #1a252f !important;
    }

    /* ========== ปรับสีข้อความเมื่อไม่มีข้อมูล ========== */
    .text-center .text-muted {
        color: #1a252f !important;
        font-weight: 500 !important;
    }

    .text-center .fa-users {
        color: #3498db !important;
    }

    /* ========== ปรับสีปุ่มในตาราง ========== */
    .btn-outline-info {
        border-color: #3498db !important;
        color: #3498db !important;
        font-weight: 500 !important;
    }

    .btn-outline-info:hover {
        background-color: #3498db !important;
        border-color: #3498db !important;
        color: #ffffff !important;
    }

    .btn-outline-primary {
        border-color: #2c3e50 !important;
        color: #2c3e50 !important;
        font-weight: 500 !important;
    }

    .btn-outline-primary:hover {
        background-color: #2c3e50 !important;
        border-color: #2c3e50 !important;
        color: #ffffff !important;
    }

    .btn-outline-danger {
        border-color: #e74c3c !important;
        color: #e74c3c !important;
        font-weight: 500 !important;
    }

    .btn-outline-danger:hover {
        background-color: #e74c3c !important;
        border-color: #e74c3c !important;
        color: #ffffff !important;
    }



    .table td {
        vertical-align: middle !important;
    }
</style>
@endsection

@section('content')
<!-- แสดงข้อความแจ้งเตือน -->
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">รายการบุคลากร</h4>
    <a href="{{ route('admin.staff.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>เพิ่มบุคลากร
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($staff->count() > 0)
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th style="width: 80px;">รูปภาพ</th>
                            <th>ชื่อ-นามสกุล</th>
                            <th>ตำแหน่ง</th>
                            <th style="width: 150px;">การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($staff as $member)
                        <tr>
                            <td>
                                @if($member->photo)
                                    <img src="{{ asset('storage/' . $member->photo) }}"
                                         alt="{{ $member->name }}"
                                         class="rounded-circle"
                                         style="width: 60px; height: 60px; object-fit: cover; object-position: center 20%;">
                                @else
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center"
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <strong>{{ $member->name }}</strong>
                            </td>
                            <td>{{ $member->position }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.staff.edit', $member->id) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.staff.destroy', $member->id) }}"
                                          method="POST"
                                          style="display: inline;"
                                          onsubmit="return confirm('คุณต้องการลบข้อมูลบุคลากร {{ $member->name }} หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="btn btn-sm btn-outline-danger"
                                                title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $staff->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-5x text-muted mb-4"></i>
                <h5 class="text-muted">ยังไม่มีข้อมูลบุคลากร</h5>
                <p class="text-muted">เริ่มต้นเพิ่มข้อมูลบุคลากรแรกของคุณ</p>
                <a href="{{ route('admin.staff.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มบุคลากร
                </a>
            </div>
        @endif
    </div>
</div>


@endsection

@section('scripts')
@endsection
