@extends('layouts.app')

@section('title', 'ข้อมูลบุคลากร - ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า')

@section('styles')
<style>
    .staff-card {
        background: #ffffff;
        border: 3px solid #ddd;
        border-radius: 0;
        text-align: center;
        padding: 0;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .staff-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* ผู้อำนวยการ - ขนาดใหญ่ที่สุด */
    .staff-card.director {
        border: 3px solid #ddd;
    }

    .staff-card.director .staff-image-container {
        width: 100%;
        height: 400px;
        background: #f8f9fa;
        border-bottom: 2px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .staff-card.director .staff-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
        border: none;
        border-radius: 0;
    }

    .staff-card.director .staff-avatar-placeholder {
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        border: none;
        border-radius: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }

    .staff-card.director .staff-avatar-placeholder i {
        font-size: 4rem;
    }

    .staff-card.director .staff-info {
        padding: 1.5rem;
    }

    .staff-card.director .staff-name {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .staff-card.director .staff-position {
        font-size: 1.1rem;
        font-weight: 500;
        color: #666;
        margin-bottom: 0;
    }

    /* รองผู้อำนวยการ - ขนาดกลาง */
    .staff-card.deputy {
        border: 3px solid #ddd;
    }

    .staff-card.deputy .staff-image-container {
        width: 100%;
        height: 320px;
        background: #f8f9fa;
        border-bottom: 2px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .staff-card.deputy .staff-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
        border: none;
        border-radius: 0;
    }

    .staff-card.deputy .staff-avatar-placeholder {
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        border: none;
        border-radius: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }

    .staff-card.deputy .staff-avatar-placeholder i {
        font-size: 3.5rem;
    }

    .staff-card.deputy .staff-info {
        padding: 1.5rem;
    }

    .staff-card.deputy .staff-name {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .staff-card.deputy .staff-position {
        font-size: 1rem;
        font-weight: 500;
        color: #666;
        margin-bottom: 0;
    }

    /* ครูผู้ช่วย - ขนาดเล็กกว่าปกติ */
    .staff-card.assistant {
        border: 3px solid #ddd;
    }

    .staff-card.assistant .staff-image-container {
        width: 100%;
        height: 400px;
        background: #f8f9fa;
        border-bottom: 2px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .staff-card.assistant .staff-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border: none;
        border-radius: 0;
    }

    .staff-card.assistant .staff-avatar-placeholder {
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        border: none;
        border-radius: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }

    .staff-card.assistant .staff-avatar-placeholder i {
        font-size: 2.5rem;
    }

    .staff-card.assistant .staff-info {
        padding: 1.5rem;
    }

    .staff-card.assistant .staff-name {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .staff-card.assistant .staff-position {
        font-size: 0.95rem;
        font-weight: 500;
        color: #666;
        margin-bottom: 0;
    }

    /* ครู - ขนาดกลาง */
    .staff-card.teacher {
        border: 3px solid #ddd;
    }

    .staff-card.teacher .staff-image-container {
        width: 100%;
        height: 240px;
        background: #f8f9fa;
        border-bottom: 2px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .staff-card.teacher .staff-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
        border: none;
        border-radius: 0;
    }

    .staff-card.teacher .staff-avatar-placeholder {
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        border: none;
        border-radius: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }

    .staff-card.teacher .staff-avatar-placeholder i {
        font-size: 3rem;
    }

    .staff-card.teacher .staff-info {
        padding: 1.5rem;
    }

    .staff-card.teacher .staff-name {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .staff-card.teacher .staff-position {
        font-size: 1rem;
        font-weight: 500;
        color: #666;
        margin-bottom: 0;
    }

    /* ขนาดปกติสำหรับตำแหน่งอื่นๆ */
    .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) {
        border: 3px solid #ddd;
    }

    .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-image-container {
        width: 100%;
        height: 240px;
        background: #f8f9fa;
        border-bottom: 2px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border: none;
        border-radius: 0;
    }

    .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-avatar-placeholder {
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        border: none;
        border-radius: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }

    .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-avatar-placeholder i {
        font-size: 3rem;
    }

    .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-info {
        padding: 1.5rem;
    }

    .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-name {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-position {
        font-size: 1rem;
        color: #666;
        margin-bottom: 0;
        font-weight: 500;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .staff-card.director .staff-image-container {
            height: 300px;
        }

        .staff-card.deputy .staff-image-container {
            height: 250px;
        }

        .staff-card.teacher .staff-image-container {
            height: 220px;
        }

        .staff-card.assistant .staff-image-container {
            height: 200px;
        }

        .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-image-container {
            height: 220px;
        }
    }

    @media (max-width: 576px) {
        .staff-card.director .staff-image-container {
            height: 250px;
        }

        .staff-card.deputy .staff-image-container {
            height: 220px;
        }

        .staff-card.teacher .staff-image-container {
            height: 200px;
        }

        .staff-card.assistant .staff-image-container {
            height: 180px;
        }

        .staff-card:not(.director):not(.deputy):not(.teacher):not(.assistant) .staff-image-container {
            height: 200px;
        }

        .staff-card .staff-info {
            padding: 1rem;
        }

        .staff-card .staff-name {
            font-size: 1rem;
        }

        .staff-card .staff-position {
            font-size: 0.85rem;
        }
    }
</style>
@endsection

@section('content')
<!-- Page Header -->
<section class="bg-success text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-users me-3"></i>
                    ข้อมูลบุคลากร
                </h1>
                <p class="lead">ทีมงานผู้เชี่ยวชาญด้านการดูแลและพัฒนาเด็กเล็ก</p>
            </div>
        </div>
    </div>
</section>

<!-- Staff Content -->
<section class="py-5">
    <div class="container">
        @if($staff->count() > 0)
            @php
                // จัดเรียงบุคลากรตามลำดับความสำคัญ
                $sortedStaff = $staff->sortBy(function($member) {
                    $position = strtolower($member->position);
                    if (str_contains($position, 'ผู้อำนวยการ') || str_contains($position, 'อำนวยการ')) {
                        return 1; // ผู้อำนวยการอันดับ 1
                    } elseif (str_contains($position, 'รอง')) {
                        return 2; // รองผู้อำนวยการอันดับ 2
                    } elseif (str_contains($position, 'ครูประจำ') || str_contains($position, 'ครูผู้ดูแล')) {
                        return 3; // ครูประจำอันดับ 3
                    } elseif (str_contains($position, 'พยาบาล')) {
                        return 4; // พยาบาลอันดับ 4
                    } elseif (str_contains($position, 'ครูผู้ช่วย') || str_contains($position, 'ผู้ช่วยครู')) {
                        return 5; // ครูผู้ช่วยอันดับ 5
                    } else {
                        return 6; // ตำแหน่งอื่นๆ อันดับ 6
                    }
                });
            @endphp

            <!-- ผู้อำนวยการ - แสดงแยกต่างหาก -->
            @php
                $directors = $sortedStaff->filter(function($member) {
                    $position = strtolower($member->position);
                    return str_contains($position, 'ผู้อำนวยการ') || str_contains($position, 'อำนวยการ');
                });
            @endphp

            @if($directors->count() > 0)
                <div class="row justify-content-center mb-5">
                    @foreach($directors as $member)
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <div class="staff-card director">
                            <div class="staff-image-container">
                                @if($member->photo)
                                    <img src="{{ asset('storage/' . $member->photo) }}"
                                         alt="{{ $member->name }}"
                                         class="staff-avatar">
                                @else
                                    <div class="staff-avatar-placeholder">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="staff-info">
                                <h5 class="staff-name">{{ $member->name }}</h5>
                                <p class="staff-position">{{ $member->position }}</p>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @endif

            <!-- บุคลากรอื่นๆ -->
            @php
                $otherStaff = $sortedStaff->filter(function($member) {
                    $position = strtolower($member->position);
                    return !str_contains($position, 'ผู้อำนวยการ') && !str_contains($position, 'อำนวยการ');
                });
            @endphp

            @if($otherStaff->count() > 0)
                <div class="row g-4">
                    @foreach($otherStaff as $member)
                    @php
                        $position = strtolower($member->position);
                        $cardClass = '';
                        $iconClass = 'fas fa-user';

                        if (str_contains($position, 'รอง')) {
                            $cardClass = 'deputy';
                            $iconClass = 'fas fa-user-tie';
                        } elseif (str_contains($position, 'ครูผู้ช่วย') || str_contains($position, 'ผู้ช่วยครู')) {
                            $cardClass = 'assistant';
                            $iconClass = 'fas fa-graduation-cap';
                        } elseif (str_contains($position, 'ครู')) {
                            $cardClass = 'teacher';
                            $iconClass = 'fas fa-chalkboard-teacher';
                        } elseif (str_contains($position, 'พยาบาล')) {
                            $iconClass = 'fas fa-user-nurse';
                        } elseif (str_contains($position, 'ครัว')) {
                            $iconClass = 'fas fa-utensils';
                        } elseif (str_contains($position, 'แม่บ้าน') || str_contains($position, 'ความสะอาด')) {
                            $iconClass = 'fas fa-broom';
                        }
                    @endphp

                    <div class="col-lg-4 col-md-6">
                        <div class="staff-card {{ $cardClass }}">
                            <div class="staff-image-container">
                                @if($member->photo)
                                    <img src="{{ asset('storage/' . $member->photo) }}"
                                         alt="{{ $member->name }}"
                                         class="staff-avatar">
                                @else
                                    <div class="staff-avatar-placeholder">
                                        <i class="{{ $iconClass }}"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="staff-info">
                                <h5 class="staff-name">{{ $member->name }}</h5>
                                <p class="staff-position">{{ $member->position }}</p>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            @endif
        @else
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-5x text-muted mb-4"></i>
                        <h3 class="text-muted">ยังไม่มีข้อมูลบุคลากร</h3>
                        <p class="text-muted">กรุณาติดตามข้อมูลในภายหลัง</p>
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>กลับหน้าหลัก
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- About Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="fw-bold text-primary mb-4">เกี่ยวกับทีมงานของเรา</h2>
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <p class="lead text-muted">
                            ทีมงานของเราประกอบด้วยผู้เชี่ยวชาญด้านการดูแลและพัฒนาเด็กเล็ก 
                            ที่มีความรู้ ความสามารถ และประสบการณ์ในการดูแลเด็กเล็กอย่างมืออาชีพ
                        </p>
                        <p class="text-muted">
                            เราให้ความสำคัญกับการพัฒนาตนเองอย่างต่อเนื่อง เพื่อให้สามารถดูแลและพัฒนาเด็กเล็ก
                            ให้เติบโตอย่างมีคุณภาพ ทั้งด้านร่างกาย จิตใจ สติปัญญา และสังคม
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row g-4 mt-4">
            <div class="col-md-4 text-center">
                <div class="bg-white p-4 rounded shadow-sm">
                    <i class="fas fa-heart fa-3x text-danger mb-3"></i>
                    <h5>ใส่ใจในทุกรายละเอียด</h5>
                    <p class="text-muted small">
                        เราให้ความสำคัญกับการดูแลเด็กในทุกด้าน ด้วยความรักและความเอาใจใส่
                    </p>
                </div>
            </div>
            
            <div class="col-md-4 text-center">
                <div class="bg-white p-4 rounded shadow-sm">
                    <i class="fas fa-star fa-3x text-warning mb-3"></i>
                    <h5>มืออาชีพ</h5>
                    <p class="text-muted small">
                        ทีมงานที่ผ่านการอบรมและมีประสบการณ์ในการดูแลเด็กเล็กอย่างมืออาชีพ
                    </p>
                </div>
            </div>
            
            <div class="col-md-4 text-center">
                <div class="bg-white p-4 rounded shadow-sm">
                    <i class="fas fa-handshake fa-3x text-success mb-3"></i>
                    <h5>ทำงานเป็นทีม</h5>
                    <p class="text-muted small">
                        การทำงานร่วมกันเป็นทีม เพื่อให้เด็กได้รับการดูแลที่ดีที่สุด
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
