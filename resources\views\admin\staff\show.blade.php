@extends('layouts.admin')

@section('title', 'ดูข้อมูลบุคลากร - ระบบจัดการ')
@section('page-title', 'ดูข้อมูลบุคลากร')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">ข้อมูลบุคลากร</h4>
    <div>
        <a href="{{ route('admin.staff.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>กลับ
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                @if($staff->photo)
                    <img src="{{ asset('storage/' . $staff->photo) }}"
                         alt="{{ $staff->name }}"
                         class="rounded-circle mb-3"
                         style="width: 200px; height: 200px; object-fit: cover; object-position: center 20%;">
                @else
                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 200px; height: 200px;">
                        <i class="fas fa-user fa-5x text-muted"></i>
                    </div>
                @endif
                
                <h5 class="card-title">{{ $staff->name }}</h5>
                <p class="card-text text-muted">{{ $staff->position }}</p>
                
                @if($staff->is_active)
                    <span class="badge bg-success fs-6">ใช้งาน</span>
                @else
                    <span class="badge bg-secondary fs-6">ไม่ใช้งาน</span>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>ข้อมูลรายละเอียด
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>ชื่อ-นามสกุล:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ $staff->name }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>ตำแหน่ง:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ $staff->position }}
                    </div>
                </div>
                

                

                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>สถานะ:</strong>
                    </div>
                    <div class="col-sm-9">
                        @if($staff->is_active)
                            <span class="badge bg-success">ใช้งาน</span>
                        @else
                            <span class="badge bg-secondary">ไม่ใช้งาน</span>
                        @endif
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>วันที่สร้าง:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ $staff->created_at->format('d/m/Y H:i') }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>วันที่แก้ไขล่าสุด:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ $staff->updated_at->format('d/m/Y H:i') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a href="{{ route('admin.staff.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>กลับไปรายการ
            </a>
            
            <div>
                <button type="button"
                        class="btn btn-danger"
                        onclick="confirmDelete({{ $staff->id }}, '{{ $staff->name }}')">
                    <i class="fas fa-trash me-2"></i>ลบ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ยืนยันการลบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>คุณแน่ใจหรือไม่ที่จะลบบุคลากร <strong id="deleteItemName"></strong>?</p>
                <p class="text-danger">การดำเนินการนี้ไม่สามารถยกเลิกได้</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>ลบ
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function confirmDelete(id, name) {
    if (confirm('คุณต้องการลบข้อมูลบุคลากร "' + name + '" หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')) {
        // สร้าง form แบบ dynamic
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/staff/' + id;

        // เพิ่ม CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // เพิ่ม method DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // เพิ่ม form ไปยัง body และ submit
        document.body.appendChild(form);
        form.submit();
    }
}


</script>
@endsection
