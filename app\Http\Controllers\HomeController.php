<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Staff;
use App\Models\ActivityImage;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // ดึงข่าวล่าสุด 3 ข่าว
        $latestNews = News::published()
            ->with(['images', 'featuredImage'])
            ->latest()
            ->take(3)
            ->get();

        // ดึงข้อมูลบุคลากร ที่เปิดใช้งาน เรียงตาม sort_order
        $staffMembers = Staff::active()
            ->ordered()
            ->get();

        // ดึงรูปภาพกิจกรรมล่าสุด 6 รูป
        $recentImages = ActivityImage::published()
            ->latest()
            ->take(6)
            ->get();

        return view('home', compact('latestNews', 'staffMembers', 'recentImages'));
    }
}
