<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NewsImage;
use Illuminate\Support\Facades\File;

class FixNewsImagePaths extends Command
{
    protected $signature = 'news:fix-image-paths';
    protected $description = 'Fix news image paths to match actual files';

    public function handle()
    {
        $this->info('Starting to fix news image paths...');

        // ดึงไฟล์ทั้งหมดในโฟลเดอร์ news/images
        $imagesDir = public_path('storage/news/images');
        $actualFiles = [];
        
        if (is_dir($imagesDir)) {
            $files = scandir($imagesDir);
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && is_file($imagesDir . '/' . $file)) {
                    $actualFiles[] = $file;
                }
            }
        }

        $this->info('Found ' . count($actualFiles) . ' actual files in news/images directory');

        $newsImages = NewsImage::all();
        $fixed = 0;
        $errors = 0;

        foreach ($newsImages as $image) {
            $this->info("Processing image ID: {$image->id}");
            
            $currentPath = str_replace('\\', '/', $image->image_path);
            $currentFilename = basename($currentPath);
            
            // ตรวจสอบว่าไฟล์ปัจจุบันมีอยู่หรือไม่
            $currentFullPath = public_path('storage/' . $currentPath);
            
            if (file_exists($currentFullPath)) {
                $this->info("  - File exists, no need to fix");
                continue;
            }

            // หาไฟล์ที่ตรงกัน
            $matchedFile = null;
            
            // ลองหาไฟล์ที่มีชื่อคล้ายกัน
            foreach ($actualFiles as $actualFile) {
                // ตรวจสอบว่าชื่อไฟล์มีส่วนที่เหมือนกันหรือไม่
                if (strpos($actualFile, '518214443') !== false && strpos($currentFilename, '518214443') !== false) {
                    $matchedFile = $actualFile;
                    break;
                } elseif (strpos($actualFile, '518247253') !== false && strpos($currentFilename, '518247253') !== false) {
                    $matchedFile = $actualFile;
                    break;
                } elseif (strpos($actualFile, '518407863') !== false && strpos($currentFilename, '518407863') !== false) {
                    $matchedFile = $actualFile;
                    break;
                } elseif (strpos($actualFile, '518566081') !== false && strpos($currentFilename, '518566081') !== false) {
                    $matchedFile = $actualFile;
                    break;
                } elseif (strpos($actualFile, '518832359') !== false && strpos($currentFilename, '518832359') !== false) {
                    $matchedFile = $actualFile;
                    break;
                } elseif (strpos($actualFile, '519345732') !== false && strpos($currentFilename, '519345732') !== false) {
                    $matchedFile = $actualFile;
                    break;
                } elseif (strpos($actualFile, '519892897') !== false && strpos($currentFilename, '519892897') !== false) {
                    $matchedFile = $actualFile;
                    break;
                }
            }

            if ($matchedFile) {
                // อัปเดต path ในฐานข้อมูล
                $newImagePath = 'news/images/' . $matchedFile;
                $newThumbnailPath = 'news/thumbnails/' . $matchedFile;
                
                $image->update([
                    'image_path' => $newImagePath,
                    'thumbnail_path' => $newThumbnailPath
                ]);
                
                $fixed++;
                $this->info("  - ✅ Fixed: {$currentPath} -> {$newImagePath}");
            } else {
                $errors++;
                $this->error("  - ❌ No matching file found for: {$currentFilename}");
            }
        }

        $this->info("\n=== Summary ===");
        $this->info("Total images processed: " . $newsImages->count());
        $this->info("Paths fixed: {$fixed}");
        $this->info("Errors: {$errors}");

        if ($fixed > 0) {
            $this->info("\n✅ Path fixing completed successfully!");
            $this->info("Now run: php artisan news:regenerate-thumbnails");
        } else {
            $this->warn("\n⚠️  No paths were fixed.");
        }

        return 0;
    }
}
