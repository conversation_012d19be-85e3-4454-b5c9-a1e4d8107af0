@extends('layouts.app')

@section('title', $category->name . ' - รูปภาพกิจกรรม - ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า')

@section('styles')
<style>
    .gallery-item {
        position: relative;
        overflow: hidden;
        border-radius: 10px;
        cursor: pointer;
    }

    .gallery-item img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        object-position: center center;
    }

    .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
        opacity: 0;
        display: flex;
        align-items: flex-end;
        padding: 20px;
        color: white;
        transition: opacity 0.3s ease;
    }

    .gallery-item:hover .gallery-overlay {
        opacity: 1;
    }

    .gallery-info h6 {
        color: white;
        margin-bottom: 5px;
    }

    .gallery-info p {
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
    }
</style>
@endsection

@section('content')
<!-- Hero Section -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-12">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="{{ route('home') }}" class="text-white-50">หน้าหลัก</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('gallery.index') }}" class="text-white-50">รูปภาพกิจกรรม</a>
                        </li>
                        <li class="breadcrumb-item active text-white" aria-current="page">
                            {{ $category->name }}
                        </li>
                    </ol>
                </nav>
                
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-images me-3"></i>
                    {{ $category->name }}
                </h1>
                @if($category->description)
                    <p class="lead">{{ $category->description }}</p>
                @endif
                <div class="mt-3">
                    <span class="badge bg-light text-primary fs-6">
                        {{ $images->total() }} รูปภาพ
                    </span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Content -->
<section class="py-5">
    <div class="container">
        @if($images->count() > 0)
            <div class="row g-4">
                @foreach($images as $image)
                <div class="col-lg-4 col-md-6">
                    <div class="gallery-item" data-bs-toggle="modal" data-bs-target="#imageModal{{ $image->id }}">
                        <img src="{{ $image->thumbnail_url ?? $image->image_url }}"
                             alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                             class="img-fluid">
                        
                        <div class="gallery-overlay">
                            <div class="gallery-info">
                                <h6 class="mb-1">{{ $category->name }}</h6>
                                @if($image->description)
                                    <p class="mb-1 small">{{ Str::limit($image->description, 60) }}</p>
                                @endif
                                <small class="text-white-50">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ $image->created_at->format('d/m/Y') }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-5">
                {{ $images->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-images fa-5x text-muted mb-4"></i>
                <h4 class="text-muted">ยังไม่มีรูปภาพในหมวดหมู่นี้</h4>
                <p class="text-muted">กรุณาติดตามรูปภาพใหม่ๆ ในอนาคต</p>
                <a href="{{ route('gallery.index') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>กลับไปดูรูปภาพทั้งหมด
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Image Modals -->
@foreach($images as $image)
<div class="modal fade" id="imageModal{{ $image->id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $category->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <img src="{{ $image->image_url }}"
                     alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                     class="img-fluid w-100">
            </div>
            @if($image->description)
            <div class="modal-footer">
                <div class="w-100">
                    <p class="mb-1">{{ $image->description }}</p>
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        {{ $image->created_at->format('d/m/Y') }}
                    </small>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endforeach
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preload images for better performance
    const images = document.querySelectorAll('.gallery-item img');
    images.forEach(img => {
        const fullSizeUrl = img.src.replace('/thumbnails/', '/');
        const preloadImg = new Image();
        preloadImg.src = fullSizeUrl;
    });
});
</script>
@endsection
