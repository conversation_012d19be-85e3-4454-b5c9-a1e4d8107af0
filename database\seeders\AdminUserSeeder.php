<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Admin;

use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // สร้างผู้ดูแลระบบ
        Admin::create([
            'name' => 'ผู้ดูแลระบบ',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);



    }
}
