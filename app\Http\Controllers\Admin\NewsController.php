<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\NewsImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class NewsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $news = News::with(['images', 'featuredImage'])
            ->latest()
            ->paginate(10);

        return view('admin.news.index', compact('news'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.news.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
            'is_published' => 'boolean'
        ]);

        $data = $request->only(['title', 'content']);
        $data['is_published'] = $request->has('is_published');

        $news = News::create($data);

        // จัดการรูปภาพหลายรูป
        if ($request->hasFile('images')) {
            $this->handleMultipleImages($request->file('images'), $news);
        }

        return redirect()->route('admin.news.index')
            ->with('success', 'ข่าวถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $news = News::with(['images', 'featuredImage'])->findOrFail($id);
        return view('admin.news.show', compact('news'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $news = News::with('images')->findOrFail($id);
        return view('admin.news.edit', compact('news'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $news = News::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
            'is_published' => 'boolean'
        ]);

        $data = $request->only(['title', 'content']);
        $data['is_published'] = $request->has('is_published');

        $news->update($data);

        // จัดการรูปภาพหลายรูป
        if ($request->hasFile('images')) {
            $this->handleMultipleImages($request->file('images'), $news);
        }

        return redirect()->route('admin.news.index')
            ->with('success', 'ข่าวถูกอัพเดทเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        \Log::info('NewsController::destroy called with ID: ' . $id);

        try {
            $news = News::findOrFail($id);
            \Log::info('News found: ' . $news->title);

            // ลบรูปภาพเก่า
            if ($news->image && Storage::disk('public')->exists($news->image)) {
                $deleted = Storage::disk('public')->delete($news->image);
                \Log::info('Deleted main image: ' . ($deleted ? 'success' : 'failed'));
            }

            // ลบรูปภาพหลายรูป
            foreach ($news->images as $image) {
                if ($image->image_path && Storage::disk('public')->exists($image->image_path)) {
                    $deleted = Storage::disk('public')->delete($image->image_path);
                    \Log::info('Deleted image: ' . ($deleted ? 'success' : 'failed'));
                }
                if ($image->thumbnail_path && Storage::disk('public')->exists($image->thumbnail_path)) {
                    $deleted = Storage::disk('public')->delete($image->thumbnail_path);
                    \Log::info('Deleted thumbnail: ' . ($deleted ? 'success' : 'failed'));
                }
            }

            $news->delete();
            \Log::info('News deleted successfully');

            return redirect()->route('admin.news.index')
                ->with('success', 'ข่าวถูกลบเรียบร้อยแล้ว');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Log::error('News not found: ' . $id);
            return redirect()->route('admin.news.index')
                ->with('error', 'ไม่พบข่าวที่ต้องการลบ');
        } catch (\Exception $e) {
            \Log::error('Error deleting news: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return redirect()->route('admin.news.index')
                ->with('error', 'เกิดข้อผิดพลาดในการลบข่าว: ' . $e->getMessage());
        }
    }

    /**
     * จัดการการอัปโหลดรูปภาพหลายรูป
     */
    private function handleMultipleImages($images, $news)
    {
        foreach ($images as $index => $image) {
            try {
                $imageData = $this->processNewsImage($image);

                NewsImage::create([
                    'news_id' => $news->id,
                    'image_path' => $imageData['image_path'],
                    'thumbnail_path' => $imageData['thumbnail_path'],
                    'sort_order' => $index,
                    'is_featured' => $index === 0 // รูปแรกเป็น featured
                ]);
            } catch (\Exception $e) {
                // Log error แต่ไม่หยุดการทำงาน
                \Log::error('Error uploading news image: ' . $e->getMessage());
            }
        }
    }

    /**
     * ประมวลผลรูปภาพข่าว
     */
    private function processNewsImage($image)
    {
        $filename = time() . '_' . uniqid() . '_' . \Str::slug(pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $image->getClientOriginalExtension();

        // อัปโหลดรูปต้นฉบับไปยังโฟลเดอร์ news/images
        $imagePath = $image->storeAs('news/images', $filename, 'public');

        // สร้าง thumbnail
        $thumbnailPath = 'news/thumbnails/' . $filename;
        $thumbnailFullPath = storage_path('app/public/' . $thumbnailPath);

        // สร้างโฟลเดอร์ thumbnail หากยังไม่มี
        if (!file_exists(dirname($thumbnailFullPath))) {
            mkdir(dirname($thumbnailFullPath), 0755, true);
        }

        // สร้าง thumbnail
        if ($this->createThumbnail(storage_path('app/public/' . $imagePath), $thumbnailFullPath, 400, 300)) {
            $thumbnailPath = $thumbnailPath;
        } else {
            $thumbnailPath = $imagePath; // ใช้รูปต้นฉบับถ้าสร้าง thumbnail ไม่ได้
        }

        // ซิงค์ไฟล์ไปยัง public/storage
        $this->syncImageToPublic($imagePath);
        if ($thumbnailPath !== $imagePath) {
            $this->syncImageToPublic($thumbnailPath);
        }

        return [
            'image_path' => $imagePath,
            'thumbnail_path' => $thumbnailPath
        ];
    }

    /**
     * สร้าง thumbnail
     */
    private function createThumbnail($sourcePath, $destinationPath, $width, $height)
    {
        if (!extension_loaded('gd')) {
            return copy($sourcePath, $destinationPath);
        }

        try {
            $imageInfo = getimagesize($sourcePath);
            if (!$imageInfo) {
                return copy($sourcePath, $destinationPath);
            }

            $mime = $imageInfo['mime'];

            switch ($mime) {
                case 'image/jpeg':
                    $source = imagecreatefromjpeg($sourcePath);
                    break;
                case 'image/png':
                    $source = imagecreatefrompng($sourcePath);
                    break;
                case 'image/gif':
                    $source = imagecreatefromgif($sourcePath);
                    break;
                default:
                    return copy($sourcePath, $destinationPath);
            }

            if (!$source) {
                return copy($sourcePath, $destinationPath);
            }

            $sourceWidth = imagesx($source);
            $sourceHeight = imagesy($source);

            // คำนวณขนาดใหม่โดยรักษาอัตราส่วน
            $ratio = min($width / $sourceWidth, $height / $sourceHeight);
            $newWidth = intval($sourceWidth * $ratio);
            $newHeight = intval($sourceHeight * $ratio);

            $thumbnail = imagecreatetruecolor($newWidth, $newHeight);

            // รักษาความโปร่งใสสำหรับ PNG
            if ($mime == 'image/png') {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefill($thumbnail, 0, 0, $transparent);
            }

            imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);

            // บันทึกไฟล์
            $result = false;
            switch ($mime) {
                case 'image/jpeg':
                    $result = imagejpeg($thumbnail, $destinationPath, 85);
                    break;
                case 'image/png':
                    $result = imagepng($thumbnail, $destinationPath);
                    break;
                case 'image/gif':
                    $result = imagegif($thumbnail, $destinationPath);
                    break;
            }

            imagedestroy($source);
            imagedestroy($thumbnail);

            return $result;

        } catch (\Exception $e) {
            return copy($sourcePath, $destinationPath);
        }
    }

    /**
     * ซิงค์ไฟล์จาก storage/app/public ไปยัง public/storage
     */
    private function syncImageToPublic($imagePath)
    {
        $sourcePath = storage_path('app/public/' . $imagePath);
        $targetPath = public_path('storage/' . $imagePath);

        // สร้างโฟลเดอร์ target หากไม่มี
        $targetDir = dirname($targetPath);
        if (!file_exists($targetDir)) {
            mkdir($targetDir, 0755, true);
        }

        // คัดลอกไฟล์
        if (file_exists($sourcePath) && !file_exists($targetPath)) {
            copy($sourcePath, $targetPath);
        }
    }

    /**
     * หน้าจัดการรูปภาพข่าวสาร
     */
    public function manageImages($id)
    {
        $news = News::with('images')->findOrFail($id);
        return view('admin.news.images', compact('news'));
    }

    /**
     * ตั้งรูปภาพเป็น featured
     */
    public function setFeatured($imageId)
    {
        try {
            $image = NewsImage::findOrFail($imageId);

            // ยกเลิก featured ของรูปอื่นๆ ในข่าวเดียวกัน
            NewsImage::where('news_id', $image->news_id)
                     ->where('id', '!=', $imageId)
                     ->update(['is_featured' => false]);

            // ตั้งรูปนี้เป็น featured
            $image->update(['is_featured' => true]);

            return response()->json([
                'success' => true,
                'message' => 'ตั้งรูปหลักเรียบร้อยแล้ว'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการตั้งรูปหลัก: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * ลบรูปภาพข่าวสาร
     */
    public function deleteImage($imageId)
    {
        try {
            $image = NewsImage::findOrFail($imageId);

            // ลบไฟล์รูปภาพจาก storage
            if ($image->image_path && Storage::disk('public')->exists($image->image_path)) {
                Storage::disk('public')->delete($image->image_path);
            }

            // ลบไฟล์ thumbnail จาก storage
            if ($image->thumbnail_path && Storage::disk('public')->exists($image->thumbnail_path)) {
                Storage::disk('public')->delete($image->thumbnail_path);
            }

            // ลบข้อมูลจากฐานข้อมูล
            $image->delete();

            return response()->json([
                'success' => true,
                'message' => 'ลบรูปภาพเรียบร้อยแล้ว'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบรูปภาพ: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle status of the specified resource.
     */
    public function toggleStatus($id)
    {
        try {
            $news = News::findOrFail($id);
            $news->is_published = !$news->is_published;
            $news->save();

            return response()->json([
                'success' => true,
                'is_published' => $news->is_published,
                'message' => $news->is_published ? 'เผยแพร่ข่าวเรียบร้อยแล้ว' : 'เปลี่ยนเป็นร่างเรียบร้อยแล้ว'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการอัพเดทสถานะ'
            ], 500);
        }
    }
}
