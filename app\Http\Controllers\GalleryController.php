<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ActivityImage;
use App\Models\ActivityCategory;

use Illuminate\Http\Request;

class GalleryController extends Controller
{
    public function index(Request $request)
    {
        $query = ActivityImage::with('category')->published()->ordered();

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        $images = $query->paginate(12)->appends($request->query());
        $categories = ActivityCategory::withCount(['images' => function($query) {
            $query->published();
        }])->active()->ordered()->get();

        // Get total count for "ทั้งหมด" button
        $totalImages = ActivityImage::published()->count();

        return view('gallery.index', compact('images', 'categories', 'totalImages'));
    }

    public function category($slug)
    {
        $category = ActivityCategory::where('slug', $slug)->where('is_active', true)->firstOrFail();

        $images = ActivityImage::where('category_id', $category->id)
            ->published()
            ->ordered()
            ->paginate(12);

        return view('gallery.category', compact('category', 'images'));
    }


}
