/* ========================================
   CSS สำหรับระบบหลังบ้าน ศูนย์พัฒนาเด็กเล็ก
   Child Care Center Admin Dashboard Styles
   ออกแบบใหม่ - แก้ไขปัญหาบัคสีและเพิ่มความสวยงาม
   ======================================== */

/* ========== ตัวแปรสีหลัก - ธีมเรียบง่าย ========== */
:root {
    /* สีหลัก - ธีมเรียบง่าย สบายตา */
    --admin-primary: #6c757d;        /* สีเทาหลัก */
    --admin-secondary: #495057;      /* สีเทาเข้ม */
    --admin-light: #f8f9fa;          /* สีเทาอ่อน */
    --admin-lighter: #ffffff;        /* สีขาว */
    --admin-dark: #343a40;           /* สีเทาเข้มมาก */

    /* สีสำหรับสถานะ - โทนเรียบง่าย */
    --success-color: #28a745;        /* เขียวสำเร็จ */
    --warning-color: #ffc107;        /* เหลืองเตือน */
    --danger-color: #dc3545;         /* แดงอันตราย */
    --info-color: #17a2b8;           /* ฟ้าข้อมูล */

    /* เงาและเอฟเฟกต์ - เรียบง่าย */
    --soft-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);

    /* สีข้อความ */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --text-white: #ffffff;

    /* สีพื้นหลัง */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-light: #e9ecef;
}

/* ========== การปรับแต่งธีมเรียบง่าย ========== */
.childcare-theme {
    font-family: 'Prompt', 'Sarabun', sans-serif;
    background: var(--bg-secondary);
    min-height: 100vh;
}

/* ========== Header และ Navigation ========== */
.admin-header {
    background: var(--bg-primary);
    padding: 1rem 0;
    box-shadow: var(--soft-shadow);
    border-bottom: 1px solid var(--bg-light);
}

.admin-header h1 {
    color: white;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
}

/* ========== Sidebar เพิ่มเติม ========== */
.sidebar-brand {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.1));
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.sidebar-brand i {
    font-size: 2.5rem;
    margin-bottom: 0.8rem;
    display: block;
    color: rgba(255, 255, 255, 0.9);
}

/* ========== Cards สำหรับเด็ก ========== */
.child-card, .card {
    background: var(--bg-primary);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--bg-light);
    overflow: hidden;
    position: relative;
}

/* Card hover effects removed */

/* ========== Stats Cards สำหรับ Dashboard ========== */
.stats-card {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-radius: 8px;
    border: 1px solid var(--bg-light);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    position: relative;
}

.stats-card.news-card {
    border-left: 4px solid var(--info-color);
}

.stats-card.staff-card {
    border-left: 4px solid var(--success-color);
}

.stats-card.category-card {
    border-left: 4px solid var(--warning-color);
}

.stats-card.image-card {
    border-left: 4px solid var(--admin-primary);
}

/* ========== ปุ่มเรียบง่าย ========== */
.btn-child, .btn {
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    border: 1px solid transparent;
    font-size: 0.875rem;
}

.btn-child-primary, .btn-primary {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
}

.btn-child-secondary, .btn-secondary {
    background: var(--admin-secondary);
    border-color: var(--admin-secondary);
    color: white;
}

.btn-child-accent, .btn-warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--text-primary);
}

.btn-child-light, .btn-info {
    background: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    border-color: var(--admin-primary);
    color: var(--admin-primary);
}

.btn-outline-secondary {
    background: transparent;
    border-color: var(--admin-secondary);
    color: var(--admin-secondary);
}

.btn-child-dark, .btn-dark {
    background: linear-gradient(135deg, var(--child-dark), #34495E);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #27AE60, #2ECC71);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #E74C3C, #C0392B);
    color: white;
}

/* ========== ตาราง เรียบง่าย ========== */
.table-child {
    background: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--bg-light);
}

.table-child thead th {
    background: var(--bg-light);
    color: var(--text-primary);
    border: none;
    padding: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.table-child tbody td {
    color: var(--text-primary);
    font-weight: 400;
    padding: 0.75rem;
    border-top: 1px solid var(--bg-light);
}

.table-child tbody td strong {
    color: var(--text-primary);
    font-weight: 600;
}

.table-child tbody td small {
    color: var(--text-secondary);
    font-weight: 400;
}

/* Table hover effects removed */

.table-child tbody tr:hover td {
    color: var(--child-dark);
}

.table-child tbody tr:hover td small {
    color: #2C3E50;
}

/* ========== Badge และ Status เรียบง่าย ========== */
.badge-child {
    border-radius: 4px;
    padding: 4px 8px;
    font-weight: 500;
    font-size: 0.75rem;
}

.badge-active {
    background: var(--success-color);
    color: white;
}

.badge-inactive {
    background: var(--danger-color);
    color: white;
}

.badge-pending {
    background: var(--warning-color);
    color: var(--text-primary);
}

/* ========== Form Elements เรียบง่าย ========== */
.form-control-child, .form-control {
    border-radius: 6px;
    border: 1px solid var(--bg-light);
    padding: 8px 12px;
    font-size: 0.875rem;
}

.form-control-child:focus, .form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

.form-label-child, .form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 6px;
    font-size: 0.875rem;
}

/* ========== Modal สำหรับเด็ก ========== */
.modal-child .modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: var(--hover-shadow);
}

.modal-child .modal-header {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    color: white;
    border-radius: 20px 20px 0 0;
    border-bottom: none;
}

.modal-child .modal-footer {
    border-top: 2px solid rgba(255, 107, 157, 0.1);
    border-radius: 0 0 20px 20px;
}

/* ========== Loading (animations removed) ========== */
.loading-child {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 107, 157, 0.3);
    border-radius: 50%;
    border-top-color: var(--admin-primary);
}

/* Animations removed */

/* ========== Responsive Design ========== */
@media (max-width: 768px) {
    .child-card {
        margin-bottom: 1rem;
    }
    
    .btn-child {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .stats-card .stats-icon {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .sidebar-brand {
        padding: 0.5rem;
    }
    
    .child-card {
        border-radius: 15px;
    }
    
    .btn-child {
        border-radius: 20px;
        padding: 8px 16px;
    }
}

/* ========== Print Styles ========== */
@media print {
    .sidebar,
    .navbar-admin,
    .btn,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* ========== Accessibility ========== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for keyboard navigation */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--admin-primary);
    outline-offset: 2px;
}
