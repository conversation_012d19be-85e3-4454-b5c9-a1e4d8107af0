<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NewsImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'news_id',
        'image_path',
        'thumbnail_path',
        'alt_text',
        'sort_order',
        'is_featured'
    ];

    protected $casts = [
        'is_featured' => 'boolean'
    ];

    public function news()
    {
        return $this->belongsTo(News::class);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    // Accessor สำหรับ URL รูปภาพ
    public function getImageUrlAttribute()
    {
        if (!$this->image_path) {
            return null;
        }

        // ใช้ cache เพื่อป้องกันการเรียกซ้ำ
        static $cache = [];
        $cacheKey = 'image_url_' . $this->id . '_' . md5($this->image_path);

        if (isset($cache[$cacheKey])) {
            return $cache[$cacheKey];
        }

        // แปลง backslash เป็น forward slash สำหรับ Windows
        $normalizedPath = str_replace('\\', '/', $this->image_path);

        // ดึงชื่อไฟล์จาก path
        $filename = basename($normalizedPath);

        // ลำดับความสำคัญของ path ที่จะลอง
        $possiblePaths = [
            'news/images/' . $filename,    // ไฟล์ในโฟลเดอร์ news/images/
            $normalizedPath,               // path ที่เก็บไว้ในฐานข้อมูล
            'news/' . $filename,           // ไฟล์ในโฟลเดอร์ news/
        ];

        // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
        foreach ($possiblePaths as $path) {
            $fullPath = public_path('storage/' . $path);
            if (file_exists($fullPath)) {
                $result = asset('storage/' . $path);
                $cache[$cacheKey] = $result;
                return $result;
            }
        }

        // ถ้าไม่พบไฟล์ ให้ใช้ path แรก
        $result = asset('storage/' . $possiblePaths[0]);
        $cache[$cacheKey] = $result;
        return $result;
    }

    // Accessor สำหรับ URL thumbnail
    public function getThumbnailUrlAttribute()
    {
        // ใช้ cache เพื่อป้องกันการเรียกซ้ำ
        static $cache = [];
        $cacheKey = 'thumbnail_url_' . $this->id . '_' . md5($this->thumbnail_path ?? '');

        if (isset($cache[$cacheKey])) {
            return $cache[$cacheKey];
        }

        if (!$this->thumbnail_path) {
            $result = $this->image_url;
            $cache[$cacheKey] = $result;
            return $result;
        }

        // แปลง backslash เป็น forward slash สำหรับ Windows
        $normalizedThumbnailPath = str_replace('\\', '/', $this->thumbnail_path);

        // ดึงชื่อไฟล์จาก thumbnail path
        $filename = basename($normalizedThumbnailPath);

        // ลำดับความสำคัญของ thumbnail path ที่จะลอง
        $possiblePaths = [
            'news/thumbnails/' . $filename,    // ไฟล์ในโฟลเดอร์ news/thumbnails/
            $normalizedThumbnailPath,          // path ที่เก็บไว้ในฐานข้อมูล
            'news/images/' . $filename,        // fallback ไปใช้รูปต้นฉบับ
        ];

        // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
        foreach ($possiblePaths as $path) {
            $fullPath = public_path('storage/' . $path);
            if (file_exists($fullPath)) {
                $result = asset('storage/' . $path);
                $cache[$cacheKey] = $result;
                return $result;
            }
        }

        // ถ้าไม่พบไฟล์ thumbnail ให้ใช้รูปต้นฉบับ
        $result = $this->image_url;
        $cache[$cacheKey] = $result;
        return $result;
    }

    /**
     * สร้าง thumbnail จากรูปต้นฉบับ
     */
    public function createThumbnailFromOriginal()
    {
        if (!$this->image_path || !$this->thumbnail_path) {
            return false;
        }

        $originalPath = public_path('storage/' . str_replace('\\', '/', $this->image_path));
        $thumbnailPath = public_path('storage/' . str_replace('\\', '/', $this->thumbnail_path));

        // ตรวจสอบว่ารูปต้นฉบับมีอยู่หรือไม่
        if (!file_exists($originalPath)) {
            return false;
        }

        // สร้างโฟลเดอร์ thumbnail หากยังไม่มี
        $thumbnailDir = dirname($thumbnailPath);
        if (!file_exists($thumbnailDir)) {
            mkdir($thumbnailDir, 0755, true);
        }

        try {
            // ใช้ Intervention Image หรือ GD library
            if (extension_loaded('gd')) {
                $this->createThumbnailWithGD($originalPath, $thumbnailPath);
                return true;
            }
        } catch (\Exception $e) {
            \Log::error('Error creating thumbnail: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * สร้าง thumbnail ด้วย GD library
     */
    private function createThumbnailWithGD($originalPath, $thumbnailPath)
    {
        $imageInfo = getimagesize($originalPath);
        $mimeType = $imageInfo['mime'];

        // สร้าง image resource จากไฟล์ต้นฉบับ
        switch ($mimeType) {
            case 'image/jpeg':
                $originalImage = imagecreatefromjpeg($originalPath);
                break;
            case 'image/png':
                $originalImage = imagecreatefrompng($originalPath);
                break;
            case 'image/gif':
                $originalImage = imagecreatefromgif($originalPath);
                break;
            default:
                throw new \Exception('Unsupported image type: ' . $mimeType);
        }

        if (!$originalImage) {
            throw new \Exception('Failed to create image resource');
        }

        // ขนาดต้นฉบับ
        $originalWidth = imagesx($originalImage);
        $originalHeight = imagesy($originalImage);

        // ขนาด thumbnail (300x300 max)
        $thumbnailSize = 300;

        // คำนวณขนาดใหม่โดยรักษาอัตราส่วน
        if ($originalWidth > $originalHeight) {
            $newWidth = $thumbnailSize;
            $newHeight = intval(($originalHeight * $thumbnailSize) / $originalWidth);
        } else {
            $newHeight = $thumbnailSize;
            $newWidth = intval(($originalWidth * $thumbnailSize) / $originalHeight);
        }

        // สร้าง thumbnail image
        $thumbnailImage = imagecreatetruecolor($newWidth, $newHeight);

        // รักษาความโปร่งใสสำหรับ PNG
        if ($mimeType === 'image/png') {
            imagealphablending($thumbnailImage, false);
            imagesavealpha($thumbnailImage, true);
        }

        // ปรับขนาดรูปภาพ
        imagecopyresampled(
            $thumbnailImage, $originalImage,
            0, 0, 0, 0,
            $newWidth, $newHeight,
            $originalWidth, $originalHeight
        );

        // บันทึกไฟล์ thumbnail
        switch ($mimeType) {
            case 'image/jpeg':
                imagejpeg($thumbnailImage, $thumbnailPath, 85);
                break;
            case 'image/png':
                imagepng($thumbnailImage, $thumbnailPath, 8);
                break;
            case 'image/gif':
                imagegif($thumbnailImage, $thumbnailPath);
                break;
        }

        // ล้าง memory
        imagedestroy($originalImage);
        imagedestroy($thumbnailImage);
    }
}
