/* ========================================
   CSS สำหรับหน้าต่างๆ ในระบบหลังบ้าน
   Admin Pages Specific Styles
   ======================================== */

/* ========== หน้า Dashboard - ออกแบบใหม่สำหรับเด็ก (ธีมสีฟ้า) ========== */
.dashboard-welcome {
    background: linear-gradient(135deg,
        #3498DB 0%,
        #5DADE2 50%,
        #85C1E9 100%);
    border-radius: 30px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(52, 152, 219, 0.3);
}

.dashboard-welcome::before {
    content: '☁️';
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 3rem;
    opacity: 0.4;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.dashboard-stats {
    margin-bottom: 3rem;
}

.dashboard-stats .row {
    margin-bottom: 2rem;
}

/* ========== Stats Cards - การ์ดสถิติที่สวยงาม ========== */
.dashboard-stats .stats-card {
    height: 100%;
    min-height: 120px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* Stats card decorative elements removed */

/* Stats card hover effects removed */

/* สีเฉพาะสำหรับแต่ละ stats card */
.stats-card.news-card {
    background: linear-gradient(135deg, #FF6B9D 0%, #FF8FA3 100%);
    color: white;
}

.stats-card.staff-card {
    background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
    color: white;
}

.stats-card.category-card {
    background: linear-gradient(135deg, #FFE66D 0%, #FFD93D 100%);
    color: #2C3E50;
}

.stats-card.image-card {
    background: linear-gradient(135deg, #A8E6CF 0%, #88D8A3 100%);
    color: white;
}

.dashboard-stats .card-body {
    padding: 2.5rem 2rem !important;
    position: relative;
    z-index: 2;
}

.dashboard-stats .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.2rem;
    opacity: 0.9;
}

.dashboard-stats h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.8rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-stats small {
    font-size: 1rem;
    opacity: 0.85;
    font-weight: 500;
}

/* ไอคอนสำหรับ stats cards */
.stats-icon {
    font-size: 3rem;
    opacity: 0.8;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.stats-card.news-card .stats-icon {
    color: rgba(255, 255, 255, 0.9);
}

.stats-card.staff-card .stats-icon {
    color: rgba(255, 255, 255, 0.9);
}

.stats-card.category-card .stats-icon {
    color: rgba(44, 62, 80, 0.8);
}

.stats-card.image-card .stats-icon {
    color: rgba(255, 255, 255, 0.9);
}

/* ========== Welcome Section - ออกแบบใหม่สำหรับเด็ก ========== */
.welcome-section {
    background: linear-gradient(135deg,
        #FF6B9D 0%,
        #4ECDC4 25%,
        #FFE66D 50%,
        #A8E6CF 75%,
        #FFB6C1 100%);
    border-radius: 35px;
    position: relative;
    overflow: hidden;
    margin-top: 2rem;
    box-shadow: 0 20px 60px rgba(255, 107, 157, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 120%;
    height: 120%;
    background:
        radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 60%),
        radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    transform: rotate(45deg);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: rotate(45deg) translateY(0px); }
    50% { transform: rotate(45deg) translateY(-10px); }
}

.welcome-section .card-body {
    padding: 4.5rem 2.5rem;
    position: relative;
    z-index: 2;
}

.welcome-section h1 {
    font-size: 2.8rem;
    text-shadow: 2px 2px 6px rgba(0,0,0,0.3);
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.welcome-section h3 {
    font-size: 2rem;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.welcome-section .lead {
    font-size: 1.4rem;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.welcome-section p {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    font-size: 1.1rem;
    line-height: 1.6;
}

/* ========== หน้าจัดการข่าวสาร ========== */
.news-management .card {
    border-left: 4px solid var(--admin-primary);
}

.news-item {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.news-item:hover {
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.2);
    transform: translateY(-3px);
}

.news-status {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.news-status.published {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    color: white;
}

.news-status.draft {
    background: linear-gradient(135deg, var(--admin-accent), var(--admin-light));
    color: white;
}

/* ========== หน้าจัดการบุคลากร ========== */
.staff-management .staff-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.staff-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--admin-primary), var(--admin-secondary));
}

/* Staff card hover effects removed */

.staff-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid var(--admin-primary);
    object-fit: cover;
}

.staff-position {
    background: linear-gradient(135deg, var(--admin-accent), var(--admin-light));
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    display: inline-block;
}

/* ========== หน้าจัดการรูปภาพ ========== */
.image-management .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.image-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    position: relative;
}

/* Image item hover effects removed */

.image-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Image zoom hover effects removed */

/* Image overlay hover effects removed */

.image-actions {
    display: flex;
    gap: 10px;
}

.image-actions .btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ========== หน้าจัดการหมวดหมู่ ========== */
.category-management .category-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border-left: 4px solid var(--admin-accent);
}

/* Category item hover effects removed */

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.category-count {
    background: linear-gradient(135deg, var(--admin-secondary), var(--admin-dark));
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* ========== Forms ========== */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 40px rgba(52, 152, 219, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    border-radius: 15px;
    border: 2px solid rgba(52, 152, 219, 0.2);
    padding: 12px 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    background: rgba(255, 255, 255, 1);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.form-text {
    font-size: 0.875rem;
    color: var(--admin-text-muted);
    margin-top: 0.25rem;
}

/* ========== File Upload ========== */
.file-upload-area {
    border: 2px dashed var(--admin-primary);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    background: rgba(255, 107, 157, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.file-upload-area:hover {
    background: rgba(52, 152, 219, 0.1);
    border-color: var(--admin-secondary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.2);
}

.file-upload-area.dragover {
    background: rgba(93, 173, 226, 0.15);
    border-color: var(--admin-accent);
    transform: scale(1.02);
}

.file-upload-icon {
    font-size: 3rem;
    color: var(--admin-primary);
    margin-bottom: 1rem;
}

/* ========== Data Tables ========== */
.data-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.data-table thead th {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    color: white;
    font-weight: 600;
    padding: 1rem;
    border: none;
}

.data-table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: rgba(52, 152, 219, 0.1);
    color: #2C3E50;
}

.data-table tbody td strong {
    color: #1B4F72;
    font-weight: 600;
}

.data-table tbody td small {
    color: #34495E;
    font-weight: 500;
}

.data-table tbody tr:hover {
    background: rgba(52, 152, 219, 0.08);
}

.data-table tbody tr:hover td {
    color: #1B4F72;
}

.data-table tbody tr:hover td small {
    color: #2C3E50;
}

/* ========== Text Colors for Better Contrast ========== */
.text-muted {
    color: #5A6C7D !important;
}

.fw-semibold {
    font-weight: 600 !important;
    color: #2C3E50 !important;
}

.fw-medium {
    font-weight: 500 !important;
    color: #34495E !important;
}

/* ========== Badge Improvements ========== */
.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #F39C12, #F7DC6F) !important;
    color: #1B4F72 !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #27AE60, #58D68D) !important;
    color: white !important;
}

/* ========== Action Buttons ========== */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-buttons .btn {
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 0.875rem;
}

.btn-edit {
    background: linear-gradient(135deg, var(--admin-accent), var(--admin-light));
    border: none;
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, #E74C3C, #C0392B);
    border: none;
    color: white;
}

.btn-view {
    background: linear-gradient(135deg, var(--admin-secondary), var(--admin-dark));
    border: none;
    color: white;
}

/* ========== Pagination ========== */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.pagination .page-link {
    border-radius: 10px;
    margin: 0 2px;
    border: none;
    color: var(--admin-primary);
    padding: 8px 16px;
}

.pagination .page-link:hover {
    background: var(--admin-primary);
    color: white;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    border: none;
}

/* ========== Responsive Design ========== */
@media (max-width: 768px) {
    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .form-container {
        padding: 1rem;
        border-radius: 15px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .staff-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .dashboard-stats .card-body {
        padding: 1.5rem 1rem !important;
    }

    .dashboard-stats h2 {
        font-size: 2rem;
    }

    .welcome-section .card-body {
        padding: 3rem 1.5rem;
    }

    .welcome-section h1 {
        font-size: 2rem;
    }

    .welcome-section h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .image-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-stats {
        margin-bottom: 1rem;
    }

    .dashboard-stats .card-body {
        padding: 1.25rem 1rem !important;
    }

    .dashboard-stats h2 {
        font-size: 1.8rem;
    }

    .dashboard-stats .card-title {
        font-size: 0.9rem;
    }

    .welcome-section .card-body {
        padding: 2.5rem 1rem;
    }

    .welcome-section h1 {
        font-size: 1.8rem;
    }

    .welcome-section h3 {
        font-size: 1.3rem;
    }

    .welcome-section .lead {
        font-size: 1.1rem;
    }

    .data-table {
        font-size: 0.875rem;
    }

    .data-table thead th,
    .data-table tbody td {
        padding: 0.75rem 0.5rem;
    }
}

/* ========== Cards และ Content Areas ========== */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 40px rgba(52, 152, 219, 0.12);
    transition: all 0.3s ease;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(52, 152, 219, 0.2);
}

.card-header {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-accent));
    color: white;
    border-bottom: none;
    padding: 1.5rem;
    font-weight: 600;
}

.card-header h5 {
    margin-bottom: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.card-body {
    padding: 2rem;
}

.card-header {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-accent));
    color: white;
    border-bottom: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}
