<?php

echo "=== แก้ไขปัญหารูปภาพข่าว ===\n\n";

// ตรวจสอบและสร้างโฟลเดอร์
$sourceDir = 'storage/app/public/news/images';
$destDir = 'public/storage/news';

if (!is_dir($destDir)) {
    mkdir($destDir, 0755, true);
    echo "สร้างโฟลเดอร์: {$destDir}\n";
}

// Copy รูปภาพจาก images folder ไปยัง news folder
if (is_dir($sourceDir)) {
    $files = scandir($sourceDir);
    $copiedCount = 0;
    
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $sourcePath = $sourceDir . '/' . $file;
            $destPath = $destDir . '/' . $file;
            
            if (is_file($sourcePath)) {
                if (copy($sourcePath, $destPath)) {
                    echo "Copy: {$file} ✓\n";
                    $copiedCount++;
                } else {
                    echo "Error copying: {$file} ✗\n";
                }
            }
        }
    }
    
    echo "\nCopy เสร็จสิ้น: {$copiedCount} ไฟล์\n";
} else {
    echo "ไม่พบโฟลเดอร์: {$sourceDir}\n";
}

// Copy thumbnails
$sourceThumbnailDir = 'storage/app/public/news/thumbnails';
$destThumbnailDir = 'public/storage/news/thumbnails';

if (!is_dir($destThumbnailDir)) {
    mkdir($destThumbnailDir, 0755, true);
    echo "สร้างโฟลเดอร์: {$destThumbnailDir}\n";
}

if (is_dir($sourceThumbnailDir)) {
    $files = scandir($sourceThumbnailDir);
    $copiedCount = 0;
    
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $sourcePath = $sourceThumbnailDir . '/' . $file;
            $destPath = $destThumbnailDir . '/' . $file;
            
            if (is_file($sourcePath)) {
                if (copy($sourcePath, $destPath)) {
                    echo "Copy thumbnail: {$file} ✓\n";
                    $copiedCount++;
                } else {
                    echo "Error copying thumbnail: {$file} ✗\n";
                }
            }
        }
    }
    
    echo "\nCopy thumbnails เสร็จสิ้น: {$copiedCount} ไฟล์\n";
}

echo "\n=== เสร็จสิ้น ===\n";
