<?php

// เชื่อมต่อฐานข้อมูลโดยตรง
$host = 'localhost';
$dbname = 'child_development_center';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== ตรวจสอบข่าว ID 17 ===\n\n";
    
    // ตรวจสอบข่าว ID 17
    $stmt = $pdo->prepare("SELECT * FROM news WHERE id = 17");
    $stmt->execute();
    $news = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($news) {
        echo "News ID: " . $news['id'] . "\n";
        echo "Title: " . $news['title'] . "\n";
        echo "Main Image: " . ($news['image'] ?? 'null') . "\n";
        
        if ($news['image']) {
            $imagePath = "public/storage/" . $news['image'];
            echo "Main Image Path: " . $imagePath . "\n";
            echo "Main Image Exists: " . (file_exists($imagePath) ? 'YES' : 'NO') . "\n";
        }
        
        // ตรวจสอบรูปภาพหลายรูป
        $stmt2 = $pdo->prepare("SELECT * FROM news_images WHERE news_id = 17 ORDER BY sort_order");
        $stmt2->execute();
        $images = $stmt2->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\nImages Count: " . count($images) . "\n";
        
        foreach ($images as $index => $image) {
            echo "\n--- Image " . ($index + 1) . " ---\n";
            echo "ID: " . $image['id'] . "\n";
            echo "Path: " . $image['image_path'] . "\n";
            echo "Sort Order: " . $image['sort_order'] . "\n";
            echo "Is Featured: " . ($image['is_featured'] ? 'YES' : 'NO') . "\n";
            
            // ตรวจสอบไฟล์
            $filename = basename($image['image_path']);
            
            // ทดสอบ path ต่างๆ
            $testPaths = [
                "public/storage/" . $image['image_path'], // path เดิม
                "public/storage/news/" . $filename, // news/filename.jpg
                "public/storage/" . str_replace('news/images/', 'news/', $image['image_path']), // แปลง path
                "public/storage/" . str_replace('images/', '', $image['image_path']), // ลบ images/
            ];
            
            echo "Testing paths:\n";
            foreach ($testPaths as $pathIndex => $testPath) {
                $exists = file_exists($testPath);
                echo "  {$pathIndex}: {$testPath} -> " . ($exists ? 'EXISTS' : 'NOT FOUND') . "\n";
                if ($exists) {
                    $url = str_replace('public/', 'http://localhost:8000/', $testPath);
                    echo "      URL: {$url}\n";
                }
            }
        }
        
        // ตรวจสอบไฟล์ในโฟลเดอร์ news
        echo "\n=== ไฟล์ในโฟลเดอร์ news ===\n";
        $newsDir = "public/storage/news";
        if (is_dir($newsDir)) {
            $files = scandir($newsDir);
            $count = 0;
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && !is_dir($newsDir . '/' . $file)) {
                    echo "File: {$file}\n";
                    $count++;
                }
            }
            echo "Total files: {$count}\n";
        }
        
    } else {
        echo "ไม่พบข่าว ID 17\n";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== เสร็จสิ้น ===\n";
