@extends('layouts.app')

@section('title', $newsItem->title . ' - ข่าวประชาสัมพันธ์')

@section('content')


<!-- News Content -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <article class="card">
                    <!-- รูปภาพหลัก -->
                    @php
                        $mainImageUrl = null;

                        // ลำดับความสำคัญ: 1. Featured image 2. รูปแรกใน collection
                        if ($newsItem->featuredImage) {
                            $mainImageUrl = $newsItem->featuredImage->image_url;
                        } elseif ($newsItem->images && $newsItem->images->count() > 0) {
                            $mainImageUrl = $newsItem->images->first()->image_url;
                        }
                    @endphp

                    @if($mainImageUrl)
                        <img src="{{ $mainImageUrl }}"
                             class="card-img-top"
                             alt="{{ $newsItem->title }}"
                             style="height: 400px; object-fit: cover; object-position: center center;">
                    @endif

                    <div class="card-body">
                        <h1 class="card-title h2 mb-4">{{ $newsItem->title }}</h1>

                        <div class="content mb-4">
                            {!! nl2br(e($newsItem->content)) !!}
                        </div>

                        <!-- รูปภาพเพิ่มเติม -->
                        @if($newsItem->images && $newsItem->images->count() > 0)
                        <div class="additional-images">
                            <h5 class="mb-3">
                                <i class="fas fa-images me-2"></i>
                                รูปภาพประกอบ ({{ $newsItem->images->count() }} รูป)
                            </h5>

                            <div class="row g-4">
                                @foreach($newsItem->images as $image)
                                <div class="col-lg-4 col-md-6">
                                    <div class="gallery-item"
                                         onclick="showImageModal('{{ $image->image_url }}', 'รูปภาพ {{ $loop->iteration }}')"
                                         style="cursor: pointer;"
                                         data-image-url="{{ $image->image_url }}"
                                         data-thumbnail-url="{{ $image->thumbnail_url ?: $image->image_url }}">
                                        <img src="{{ $image->thumbnail_url ?: $image->image_url }}"
                                             alt="รูปภาพ {{ $loop->iteration }}"
                                             class="img-fluid gallery-thumbnail"
                                             onerror="console.error('Failed to load thumbnail:', this.src)">
                                        <div class="gallery-overlay">
                                            <i class="fas fa-search-plus"></i>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>
                </article>


                
                <!-- Navigation -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('news.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปยังข่าวทั้งหมด
                    </a>
                    
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>พิมพ์
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="shareNews()">
                            <i class="fas fa-share-alt me-2"></i>แชร์
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">รูปภาพประกอบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center position-relative">
                <img id="modalImage" src="" alt="" class="img-fluid" style="max-height: 70vh;">
                <!-- ปุ่มปิดภาพ -->
                <button type="button" class="btn-close position-absolute"
                        style="top: 10px; right: 10px; background: rgba(255,255,255,0.8); border-radius: 50%; width: 40px; height: 40px; z-index: 10;"
                        data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* Gallery Styles */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    cursor: pointer;
    border: 2px solid transparent;
}

/* Gallery hover effects removed */

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    object-position: center center;
    transition: none !important; /* ป้องกันการกระพริบ */
    will-change: auto;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
}

/* Gallery overlay hover removed */

.gallery-overlay i {
    color: white;
    font-size: 2rem;
}

/* Additional Images Section */
.additional-images {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.additional-images h5 {
    color: #495057;
    font-weight: 600;
}

/* Modal Fallback Styles */
.modal.show {
    display: block !important;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    opacity: 0.5;
}

body.modal-open {
    overflow: hidden;
}





/* ป้องกันการกระพริบของรูปภาพทั่วไป */
img.loaded {
    opacity: 1 !important;
    transition: none !important;
}

img.image-loading {
    opacity: 0.8;
}

/* ป้องกันการกระพริบเมื่อ hover */
.gallery-item img {
    will-change: auto; /* ลบ will-change เพื่อป้องกันการกระพริบ */
}


</style>
@endsection

@section('scripts')
<script>
function shareNews() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $newsItem->title }}',
            text: '{{ Str::limit(strip_tags($newsItem->content), 100) }}',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(function() {
            alert('ลิงก์ถูกคัดลอกแล้ว!');
        });
    }
}

// ฟังก์ชันสำหรับ Modal รูปภาพ
function showImageModal(imageUrl, imageAlt) {
    console.log('showImageModal called:', imageUrl, imageAlt);

    const modalElement = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('imageModalLabel');

    if (!modalElement) {
        console.error('Modal element not found');
        return;
    }

    if (!modalImage) {
        console.error('Modal image element not found');
        return;
    }

    if (!modalTitle) {
        console.error('Modal title element not found');
        return;
    }

    // ตั้งค่ารูปภาพ
    modalImage.src = imageUrl;
    modalImage.alt = imageAlt;
    modalTitle.textContent = imageAlt;

    // แสดง modal
    try {
        // ตรวจสอบว่า Bootstrap พร้อมใช้งานหรือไม่
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            console.log('Using Bootstrap Modal');
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else {
            console.log('Bootstrap not available, using fallback');
            // Fallback: แสดง modal แบบง่าย
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
            document.body.classList.add('modal-open');

            // ลบ backdrop เก่าก่อน (ถ้ามี)
            const existingBackdrop = document.querySelector('.modal-backdrop');
            if (existingBackdrop) {
                existingBackdrop.remove();
            }

            // เพิ่ม backdrop ใหม่
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            backdrop.onclick = function() {
                hideImageModal();
            };
            document.body.appendChild(backdrop);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
    }
}

function hideImageModal() {
    console.log('hideImageModal called');
    const modalElement = document.getElementById('imageModal');

    if (!modalElement) {
        console.error('Modal element not found');
        return;
    }

    modalElement.style.display = 'none';
    modalElement.classList.remove('show');
    document.body.classList.remove('modal-open');

    // ลบ backdrop
    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
        backdrop.remove();
    }
    document.body.classList.remove('modal-open');

    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
        backdrop.remove();
    }
}





// เพิ่ม lazy loading สำหรับรูปภาพ
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');

    // รอให้ Bootstrap โหลดเสร็จ
    setTimeout(function() {

    // ป้องกันการกระพริบของรูปภาพทั้งหมด
    const allImages = document.querySelectorAll('img');
    console.log('Found images:', allImages.length);

    allImages.forEach(img => {
        img.addEventListener('load', function() {
            this.style.opacity = '1';
            this.style.transition = 'none';
        });

        img.addEventListener('error', function() {
            console.error('Image failed to load:', this.src);
            this.style.opacity = '0.5';
        });
    });

    // ตรวจสอบ gallery items
    const galleryItems = document.querySelectorAll('.gallery-item');
    console.log('Found gallery items:', galleryItems.length);

    galleryItems.forEach((item, index) => {
        const imageUrl = item.getAttribute('data-image-url');
        const thumbnailUrl = item.getAttribute('data-thumbnail-url');
        console.log(`Gallery item ${index + 1}:`, {
            element: item,
            imageUrl: imageUrl,
            thumbnailUrl: thumbnailUrl
        });

        item.addEventListener('click', function() {
            console.log('Gallery item clicked:', index + 1);
            console.log('Image URL:', imageUrl);
        });
    });

    // ตรวจสอบ modal element
    const modalElement = document.getElementById('imageModal');
    console.log('Modal element found:', !!modalElement);

    if (modalElement) {
        console.log('Modal HTML:', modalElement.outerHTML.substring(0, 200) + '...');
    }

    // ตรวจสอบ Bootstrap
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap Modal available:', typeof bootstrap.Modal !== 'undefined');
    }

    // เพิ่ม global function สำหรับ debug
    window.testModal = function() {
        showImageModal('https://via.placeholder.com/800x600/007bff/ffffff?text=Test+Image', 'Test Image');
    };

    console.log('You can test modal by calling: testModal()');

    }, 1000); // เพิ่มเวลารอให้ Bootstrap โหลดเสร็จ



    // เพิ่ม event listener สำหรับปุ่มปิด modal
    const closeButtons = document.querySelectorAll('[data-bs-dismiss="modal"]');
    closeButtons.forEach(button => {
        button.addEventListener('click', hideImageModal);
    });

    // Keyboard navigation สำหรับ modal
    document.addEventListener('keydown', function(e) {
        const modal = document.getElementById('imageModal');
        if (modal.classList.contains('show')) {
            if (e.key === 'Escape') {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                modalInstance.hide();
            }
        }
    });

    }, 100); // รอ 100ms ให้ Bootstrap โหลดเสร็จ
});
</script>
@endsection
