@extends('layouts.admin')

@section('title', 'แก้ไขรูปภาพ - ระบบจัดการ')
@section('page-title', 'แก้ไขรูปภาพกิจกรรม')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>แก้ไขรูปภาพ
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.images.update', $image->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    

                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย</label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description"
                                  name="description"
                                  rows="3">{{ old('description', $image->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">อธิบายเกี่ยวกับรูปภาพนี้</div>
                    </div>

                    <div class="mb-3">
                        <label for="category_id" class="form-label">หมวดหมู่</label>
                        <select class="form-select @error('category_id') is-invalid @enderror"
                                id="category_id"
                                name="category_id">
                            <option value="">-- เลือกหมวดหมู่ --</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}"
                                        {{ old('category_id', $image->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">เลือกหมวดหมู่ที่เหมาะสมสำหรับรูปภาพนี้</div>
                    </div>
                    

                    
                    <!-- รูปภาพปัจจุบัน -->
                    <div class="mb-3">
                        <label class="form-label">รูปภาพปัจจุบัน</label>
                        <div class="text-center p-3 border rounded">
                            @php
                                $currentImageUrl = null;
                                if ($image->thumbnail_path) {
                                    $currentImageUrl = asset('storage/' . $image->thumbnail_path);
                                } elseif ($image->image_path) {
                                    $currentImageUrl = asset('storage/' . $image->image_path);
                                }
                            @endphp

                            @if($currentImageUrl)
                                <img src="{{ $currentImageUrl }}"
                                     alt="รูปภาพ"
                                     class="img-fluid rounded"
                                     style="max-height: 200px;"
                                     onerror="this.src='https://via.placeholder.com/200x200/e9ecef/6c757d?text=ไม่พบรูปภาพ'; console.error('Failed to load image:', this.src);">
                            @else
                                <div class="text-muted">
                                    <i class="fas fa-image fa-3x mb-2"></i>
                                    <p>ไม่พบรูปภาพ</p>
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    <!-- อัพโหลดรูปภาพใหม่ -->
                    <div class="mb-3">
                        <label for="new_image" class="form-label">เปลี่ยนรูปภาพ</label>
                        <input type="file" 
                               class="form-control @error('new_image') is-invalid @enderror" 
                               id="new_image" 
                               name="new_image" 
                               accept="image/*"
                               onchange="previewImage(this)">
                        @error('new_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">ไฟล์ที่รองรับ: JPG, PNG, GIF (ขนาดไม่เกิน 5MB)</div>
                        
                        <!-- Preview รูปภาพใหม่ -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปภาพใหม่:</label>
                            <div class="text-center p-3 border rounded">
                                <img id="preview" class="img-fluid rounded" style="max-height: 200px;">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">สถานะ</label>
                        <div class="form-check">
                            <input class="form-check-input"
                                   type="checkbox"
                                   id="is_published"
                                   name="is_published"
                                   value="1"
                                   {{ old('is_published', $image->is_published) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_published">
                                เผยแพร่รูปภาพ
                            </label>
                        </div>
                        <div class="form-text">หากไม่เลือก รูปภาพจะถูกบันทึกเป็นร่าง</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.images.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>กลับ
                        </a>
                        <div>
                            <a href="{{ route('admin.images.show', $image->id) }}" class="btn btn-info">
                                <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function previewImage(input) {
    console.log('previewImage called');
    const preview = document.getElementById('preview');
    const previewContainer = document.getElementById('imagePreview');

    if (!preview || !previewContainer) {
        console.error('Preview elements not found');
        return;
    }

    if (input.files && input.files[0]) {
        const file = input.files[0];
        console.log('File selected:', file.name, 'Size:', file.size);

        // ตรวจสอบขนาดไฟล์
        if (file.size > 5 * 1024 * 1024) { // 5MB
            alert('ไฟล์รูปภาพมีขนาดใหญ่เกิน 5MB กรุณาเลือกไฟล์ที่มีขนาดเล็กกว่า');
            input.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        // ตรวจสอบประเภทไฟล์
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('กรุณาเลือกไฟล์รูปภาพ (JPG, PNG, GIF) เท่านั้น');
            input.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        const reader = new FileReader();

        reader.onload = function(e) {
            console.log('File loaded successfully');
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        }

        reader.onerror = function() {
            console.error('Error reading file');
            alert('เกิดข้อผิดพลาดในการอ่านไฟล์');
            previewContainer.style.display = 'none';
        }

        reader.readAsDataURL(file);
    } else {
        console.log('No file selected');
        previewContainer.style.display = 'none';
    }
}

// เพิ่ม event listener เมื่อหน้าโหลดเสร็จ
document.addEventListener('DOMContentLoaded', function() {
    console.log('Edit image page loaded');

    // ตรวจสอบ elements
    const fileInput = document.getElementById('new_image');
    const preview = document.getElementById('preview');
    const previewContainer = document.getElementById('imagePreview');

    console.log('File input found:', !!fileInput);
    console.log('Preview found:', !!preview);
    console.log('Preview container found:', !!previewContainer);
});
</script>
@endsection
