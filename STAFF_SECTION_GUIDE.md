# 👥 คู่มือส่วนแสดงบุคลากรในหน้าหลัก

## 📋 ภาพรวม

ส่วนแสดงบุคลากรในหน้าหลักจะแสดงทีมงานของศูนย์พัฒนาเด็กเล็กในรูปแบบกรอบวงกลมที่สวยงาม พร้อมชื่อและตำแหน่งงาน

## ✨ คุณสมบัติที่เพิ่มใหม่

### 🎨 การออกแบบ
- **กรอบวงกลม**: รูปภาพบุคลากรในกรอบวงกลมขนาด 150x150 พิกเซล
- **เอฟเฟกต์ Hover**: เมื่อเลื่อนเมาส์จะมีการขยายและเงาที่สวยงาม
- **Animation**: แต่ละการ์ดจะเลื่อนขึ้นมาทีละใบ
- **Gradient Background**: พื้นหลังไล่สีที่นุ่มนวล
- **Responsive**: ปรับขนาดอัตโนมัติตามหน้าจอ

### 📱 การแสดงผล
- **Desktop**: 3 คอลัมน์ต่อแถว (6 คน รวม 2 แถว)
- **Tablet**: 2 คอลัมน์ต่อแถว
- **Mobile**: 1 คอลัมน์ต่อแถว

### 🔄 ระบบ Fallback
หากไม่มีข้อมูลบุคลากรในระบบ จะแสดงตัวอย่างบุคลากร 6 ตำแหน่ง:

1. **ผู้อำนวยการศูนย์** - ผู้อำนวยการ
2. **ครูประจำชั้น** - ครูผู้ดูแลเด็ก  
3. **พยาบาลประจำศูนย์** - พยาบาลวิชาชีพ
4. **ครูผู้ช่วย** - ผู้ช่วยครูผู้ดูแลเด็ก
5. **ครูครัว** - ผู้ดูแลโภชนาการ
6. **แม่บ้าน** - ผู้ดูแลความสะอาด

## 🎯 การทำงานของระบบ

### การดึงข้อมูล
```php
// ดึงข้อมูลบุคลากร 6 คนแรก
$staffMembers = Staff::orderBy('created_at', 'asc')
    ->take(6)
    ->get();
```

### การแสดงผล
- หากมีข้อมูลบุคลากร: แสดงข้อมูลจริงจากฐานข้อมูล
- หากไม่มีข้อมูล: แสดงตัวอย่างพร้อมไอคอน
- หากมีบุคลากรมากกว่า 6 คน: แสดงปุ่ม "ดูทีมงานทั้งหมด"

## 🎨 รายละเอียดการออกแบบ

### สีและธีม
- **สีหลัก**: ไล่สีน้ำเงิน-ม่วง (#667eea ถึง #764ba2)
- **สีข้อความ**: #2c3e50 (ชื่อ), #7f8c8d (ตำแหน่ง)
- **พื้นหลัง**: ไล่สีเทาอ่อน (#f8f9fa ถึง #e9ecef)

### เอฟเฟกต์พิเศษ
- **Hover Scale**: ขยาย 1.05 เท่า
- **Shadow**: เงาที่เพิ่มขึ้นเมื่อ hover
- **Underline Animation**: เส้นใต้ตำแหน่งที่ขยายออก
- **Floating Background**: พื้นหลังเคลื่อนไหวเบาๆ

### ไอคอนสำหรับตำแหน่งต่างๆ
- **ผู้อำนวยการ**: `fa-user-tie`
- **ครู**: `fa-chalkboard-teacher`
- **พยาบาล**: `fa-user-nurse`
- **ผู้ช่วย**: `fa-graduation-cap`
- **ครูครัว**: `fa-utensils`
- **แม่บ้าน**: `fa-broom`
- **ทั่วไป**: `fa-user`

## 📱 Responsive Design

### Desktop (≥992px)
- กรอบรูป: 150x150px
- ไอคอน: 3rem
- ชื่อ: 1.2rem
- ตำแหน่ง: 0.95rem

### Tablet (768px-991px)
- กรอบรูป: 120px
- ไอคอน: 2.5rem
- ชื่อ: 1.1rem
- ตำแหน่ง: 0.9rem

### Mobile (≤576px)
- กรอบรูป: 100px
- ไอคอน: 2rem
- ชื่อ: 1rem
- ตำแหน่ง: 0.85rem

## 🔧 การปรับแต่ง

### เปลี่ยนจำนวนบุคลากรที่แสดง
แก้ไขใน `HomeController.php`:
```php
$staffMembers = Staff::orderBy('created_at', 'asc')
    ->take(9) // เปลี่ยนจาก 6 เป็น 9
    ->get();
```

### เปลี่ยนการเรียงลำดับ
```php
// เรียงตามชื่อ A-Z
$staffMembers = Staff::orderBy('name', 'asc')->take(6)->get();

// เรียงตามตำแหน่ง
$staffMembers = Staff::orderBy('position', 'asc')->take(6)->get();
```

### เพิ่มข้อมูลเพิ่มเติม
แก้ไขใน `home.blade.php` เพื่อแสดงข้อมูลเพิ่มเติม:
```html
<h5 class="staff-name">{{ $staff->name }}</h5>
<p class="staff-position">{{ $staff->position }}</p>
<p class="staff-department">{{ $staff->department }}</p> <!-- เพิ่มแผนก -->
```

## 🎭 Animation และ Transition

### Fade In Animation
```css
.staff-card {
    animation: fadeInUp 1s ease-out;
}

.staff-card:nth-child(1) { animation-delay: 0.1s; }
.staff-card:nth-child(2) { animation-delay: 0.2s; }
/* ... */
```

### Hover Effects
```css
.staff-card:hover {
    transform: translateY(-10px);
}

.staff-card:hover .staff-avatar {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}
```

## 🔗 การเชื่อมโยงกับระบบอื่น

### ลิงก์ไปหน้าบุคลากร
หากมีบุคลากรมากกว่า 6 คน จะแสดงปุ่ม:
```html
<a href="{{ route('staff.index') }}" class="btn btn-outline-primary btn-lg">
    ดูทีมงานทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
</a>
```

### การจัดการข้อมูลบุคลากร
- เพิ่ม/แก้ไข/ลบ: ผ่านระบบหลังบ้าน `/admin/staff`
- อัพโหลดรูปภาพ: ผ่านฟอร์มแก้ไขบุคลากร
- จัดลำดับ: ตามวันที่สร้างข้อมูล

## 🎨 เคล็ดลับการออกแบบ

### การถ่ายรูปบุคลากร
- **ขนาด**: อย่างน้อย 300x300 พิกเซล
- **รูปแบบ**: รูปสี่เหลี่ยมจัตุรัส (จะถูกครอปเป็นวงกลม)
- **พื้นหลัง**: พื้นหลังเรียบๆ หรือเบลอ
- **การแต่งกาย**: เป็นทางการหรือชุดทำงาน
- **ท่าทาง**: ยิ้มแย้มแจ่มใส เป็นมิตร

### การเขียนข้อมูล
- **ชื่อ**: ใช้ชื่อเต็มหรือชื่อเล่น
- **ตำแหน่ง**: ใช้ชื่อตำแหน่งที่เข้าใจง่าย
- **ความยาว**: ชื่อไม่เกิน 20 ตัวอักษร, ตำแหน่งไม่เกิน 30 ตัวอักษร

## 🚀 การพัฒนาต่อ

### คุณสมบัติที่อาจเพิ่มในอนาคต
- **Modal รายละเอียด**: คลิกเพื่อดูข้อมูลเพิ่มเติม
- **Social Links**: ลิงก์ไปยัง Facebook, Line
- **ประสบการณ์**: แสดงจำนวนปีประสบการณ์
- **ความเชี่ยวชาญ**: แสดงความถนัดพิเศษ
- **การศึกษา**: แสดงวุฒิการศึกษา

### การปรับปรุงประสิทธิภาพ
- **Lazy Loading**: โหลดรูปภาพเมื่อเลื่อนมาถึง
- **Image Optimization**: บีบอัดรูปภาพอัตโนมัติ
- **Caching**: เก็บข้อมูลในแคช

## 📞 การสนับสนุน

หากต้องการความช่วยเหลือในการปรับแต่งส่วนบุคลากร กรุณาติดต่อผู้ดูแลระบบ
