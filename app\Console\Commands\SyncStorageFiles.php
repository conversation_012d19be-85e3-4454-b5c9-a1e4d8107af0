<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SyncStorageFiles extends Command
{
    protected $signature = 'storage:sync';
    protected $description = 'Sync files from storage/app/public to public/storage';

    public function handle()
    {
        $sourcePath = storage_path('app/public');
        $targetPath = public_path('storage');

        // สร้างโฟลเดอร์ target หากไม่มี
        if (!File::exists($targetPath)) {
            File::makeDirectory($targetPath, 0755, true);
        }

        // Copy ไฟล์ทั้งหมด
        $this->copyDirectory($sourcePath, $targetPath);

        $this->info('Storage files synced successfully!');
    }

    private function copyDirectory($source, $target)
    {
        if (!File::exists($source)) {
            return;
        }

        if (!File::exists($target)) {
            File::makeDirectory($target, 0755, true);
        }

        $files = File::allFiles($source);
        $directories = File::directories($source);

        // Copy ไฟล์
        foreach ($files as $file) {
            $relativePath = str_replace($source, '', $file->getPathname());
            $targetFile = $target . $relativePath;
            
            // สร้างโฟลเดอร์หากไม่มี
            $targetDir = dirname($targetFile);
            if (!File::exists($targetDir)) {
                File::makeDirectory($targetDir, 0755, true);
            }

            File::copy($file->getPathname(), $targetFile);
        }

        // Copy โฟลเดอร์
        foreach ($directories as $directory) {
            $relativePath = str_replace($source, '', $directory);
            $this->copyDirectory($directory, $target . $relativePath);
        }
    }
}
