@extends('layouts.admin')

@section('title', 'จัดการ Banner Slider')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">จัดการ Banner Slider</h3>
                    <a href="{{ route('admin.banners.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> เพิ่ม Banner
                    </a>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($banners->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped" id="bannersTable">
                                <thead>
                                    <tr>
                                        <th width="50">ลำดับ</th>
                                        <th width="100">รูปภาพ</th>
                                        <th>ชื่อ Banner</th>
                                        <th>คำบรรยาย</th>
                                        <th width="150">จัดการ</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable">
                                    @foreach($banners as $banner)
                                    <tr data-id="{{ $banner->id }}">
                                        <td>
                                            <span class="badge bg-secondary">{{ $banner->sort_order }}</span>
                                            <i class="fas fa-grip-vertical text-muted ms-2" style="cursor: move;"></i>
                                        </td>
                                        <td>
                                            <img src="{{ $banner->image_url }}" 
                                                 alt="{{ $banner->title }}" 
                                                 class="img-thumbnail" 
                                                 style="width: 80px; height: 50px; object-fit: cover;">
                                        </td>
                                        <td>
                                            <strong>{{ $banner->title }}</strong>
                                            @if($banner->subtitle)
                                                <br><small class="text-muted">{{ $banner->subtitle }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($banner->description)
                                                {{ Str::limit($banner->description, 100) }}
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.banners.show', $banner) }}" 
                                                   class="btn btn-sm btn-info" title="ดู">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.banners.edit', $banner) }}" 
                                                   class="btn btn-sm btn-warning" title="แก้ไข">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-sm btn-danger" 
                                                        title="ลบ"
                                                        onclick="deleteBanner({{ $banner->id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">ยังไม่มี Banner</h5>
                            <p class="text-muted">เริ่มต้นสร้าง Banner แรกของคุณ</p>
                            <a href="{{ route('admin.banners.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> เพิ่ม Banner
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ยืนยันการลบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>คุณแน่ใจหรือไม่ที่จะลบ Banner นี้?</p>
                <p class="text-danger"><small>การดำเนินการนี้ไม่สามารถย้อนกลับได้</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>ลบ
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sortable functionality
    const sortable = Sortable.create(document.getElementById('sortable'), {
        handle: '.fa-grip-vertical',
        animation: 150,
        onEnd: function(evt) {
            updateOrder();
        }
    });

    // Update order function
    function updateOrder() {
        const rows = document.querySelectorAll('#sortable tr');
        const banners = [];
        
        rows.forEach((row, index) => {
            banners.push({
                id: row.dataset.id,
                sort_order: index + 1
            });
        });

        fetch('{{ route("admin.banners.update-order") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ banners: banners })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update sort order badges
                rows.forEach((row, index) => {
                    row.querySelector('.badge').textContent = index + 1;
                });
                
                showToast('อัพเดทลำดับเรียบร้อยแล้ว', 'success');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('เกิดข้อผิดพลาดในการอัพเดทลำดับ', 'error');
        });
    }


});

// Delete banner function
function deleteBanner(id) {
    if (confirm('คุณต้องการลบ Banner นี้หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')) {
        // สร้าง form แบบ dynamic
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/banners/' + id;

        // เพิ่ม CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // เพิ่ม method DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // เพิ่ม form ไปยัง body และ submit
        document.body.appendChild(form);
        form.submit();
    }
}



// Toast notification function
function showToast(message, type) {
    // Simple toast implementation
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
@endsection
