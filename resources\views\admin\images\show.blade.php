@extends('layouts.admin')

@section('title', 'รายละเอียดรูปภาพ - ระบบจัดการ')
@section('page-title', 'รายละเอียดรูปภาพกิจกรรม')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-image me-2"></i>รายละเอียดรูปภาพ
                </h5>
                <div>
                    <span class="badge {{ $image->is_published ? 'bg-success' : 'bg-warning' }}">
                        {{ $image->is_published ? 'เผยแพร่แล้ว' : 'ร่าง' }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <!-- รูปภาพ -->
                <div class="text-center mb-4">
                    <img src="{{ $image->image_url }}"
                         alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                         class="img-fluid rounded shadow"
                         style="max-height: 500px;">
                </div>
                
                <!-- รายละเอียด -->
                
                @if($image->description)
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>คำอธิบาย:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ $image->description }}
                    </div>
                </div>
                @endif
                

                

                

                

                
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.images.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับ
                    </a>
                    <div>
                        <a href="{{ route('admin.images.edit', $image->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>แก้ไข
                        </a>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash me-2"></i>ลบ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>ข้อมูลไฟล์
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>ไฟล์ต้นฉบับ:</strong><br>
                    <a href="{{ asset('storage/' . $image->image_path) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i>ดูขนาดเต็ม
                    </a>
                </div>
                
                @if($image->thumbnail_path)
                <div class="mb-3">
                    <strong>Thumbnail:</strong><br>
                    <img src="{{ asset('storage/' . $image->thumbnail_path) }}" 
                         alt="Thumbnail"
                         class="img-fluid rounded"
                         style="max-width: 150px;">
                </div>
                @endif
                
                <div class="mb-3">
                    <strong>สถานะ:</strong><br>
                    <span class="badge {{ $image->is_published ? 'bg-success' : 'bg-warning' }}">
                        {{ $image->is_published ? 'เผยแพร่แล้ว' : 'ร่าง' }}
                    </span>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-link me-2"></i>ลิงก์ที่เกี่ยวข้อง
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('gallery.index') }}" target="_blank" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-eye me-2"></i>ดูในเว็บไซต์
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ยืนยันการลบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    การลบจะไม่สามารถกู้คืนได้ และไฟล์รูปภาพจะถูกลบออกจากเซิร์ฟเวอร์
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <form action="{{ route('admin.images.destroy', $image->id) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">ลบ</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
