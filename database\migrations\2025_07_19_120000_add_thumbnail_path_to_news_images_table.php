<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('news_images', function (Blueprint $table) {
            if (!Schema::hasColumn('news_images', 'thumbnail_path')) {
                $table->string('thumbnail_path')->nullable()->after('image_path');
            }
            if (!Schema::hasColumn('news_images', 'alt_text')) {
                $table->string('alt_text')->nullable()->after('thumbnail_path');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('news_images', function (Blueprint $table) {
            if (Schema::hasColumn('news_images', 'thumbnail_path')) {
                $table->dropColumn('thumbnail_path');
            }
            if (Schema::hasColumn('news_images', 'alt_text')) {
                $table->dropColumn('alt_text');
            }
        });
    }
};
