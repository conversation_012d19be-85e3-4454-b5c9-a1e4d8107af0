@extends('layouts.admin')

@section('title', 'แก้ไขหมวดหมู่รูปภาพ - ระบบจัดการ')
@section('page-title', 'แก้ไขหมวดหมู่รูปภาพกิจกรรม')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">แก้ไขหมวดหมู่: {{ $category->name }}</h4>
    <div>
        <a href="{{ route('admin.categories.show', $category) }}" class="btn btn-info me-2">
            <i class="fas fa-images me-2"></i>ดูรูปภาพ
        </a>
        <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>กลับ
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.categories.update', $category) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control @error('name') is-invalid @enderror" 
                               id="name" 
                               name="name" 
                               value="{{ old('name', $category->name) }}" 
                               required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">Slug</label>
                        <input type="text" 
                               class="form-control @error('slug') is-invalid @enderror" 
                               id="slug" 
                               name="slug" 
                               value="{{ old('slug', $category->slug) }}">
                        <div class="form-text">URL-friendly version ของชื่อหมวดหมู่</div>
                        @error('slug')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="3"
                                  placeholder="คำอธิบายเกี่ยวกับหมวดหมู่นี้">{{ old('description', $category->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดงผล</label>
                        <input type="number" 
                               class="form-control @error('sort_order') is-invalid @enderror" 
                               id="sort_order" 
                               name="sort_order" 
                               value="{{ old('sort_order', $category->sort_order) }}" 
                               min="0">
                        <div class="form-text">ตัวเลขที่น้อยกว่าจะแสดงก่อน</div>
                        @error('sort_order')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   {{ old('is_active', $category->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                เปิดใช้งาน
                            </label>
                        </div>
                        <div class="form-text">หมวดหมู่ที่ปิดใช้งานจะไม่แสดงในหน้าเว็บไซต์</div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการเปลี่ยนแปลง
                        </button>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                            ยกเลิก
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>ข้อมูลหมวดหมู่
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-1 text-primary">{{ $category->images->count() }}</div>
                            <div class="small text-muted">รูปภาพ</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 mb-1 text-info">{{ $category->sort_order }}</div>
                            <div class="small text-muted">ลำดับ</div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="small">
                    <div class="mb-2">
                        <strong>สร้างเมื่อ:</strong><br>
                        {{ $category->created_at->format('d/m/Y H:i') }}
                    </div>
                    <div class="mb-2">
                        <strong>แก้ไขล่าสุด:</strong><br>
                        {{ $category->updated_at->format('d/m/Y H:i') }}
                    </div>
                    <div>
                        <strong>สถานะ:</strong><br>
                        @if($category->is_active)
                            <span class="badge bg-success">เปิดใช้งาน</span>
                        @else
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        @if($category->images->count() > 0)
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-images me-2"></i>รูปภาพล่าสุด
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    @foreach($category->images->take(4) as $image)
                    <div class="col-6">
                        <img src="{{ $image->thumbnail_url }}" 
                             alt="{{ $image->description }}" 
                             class="img-fluid rounded"
                             style="height: 60px; width: 100%; object-fit: cover;">
                    </div>
                    @endforeach
                </div>
                @if($category->images->count() > 4)
                <div class="text-center mt-2">
                    <small class="text-muted">และอีก {{ $category->images->count() - 4 }} รูป</small>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    const originalSlug = slugInput.value;
    
    nameInput.addEventListener('input', function() {
        // Only auto-generate if slug hasn't been manually changed
        if (slugInput.value === originalSlug || !slugInput.value) {
            const slug = this.value
                .toLowerCase()
                .replace(/[^a-z0-9ก-๙\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            
            slugInput.value = slug;
        }
    });
});
</script>
@endsection
