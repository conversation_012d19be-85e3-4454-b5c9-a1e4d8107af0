@if ($paginator->hasPages())
    <nav aria-label="Pagination Navigation" class="pagination-wrapper">
        <div class="pagination-container">
            <ul class="pagination modern-pagination admin-pagination">
                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link prev-next">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-md-inline">ก่อนหน้า</span>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link prev-next" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-md-inline">ก่อนหน้า</span>
                        </a>
                    </li>
                @endif

                {{-- Pagination Elements --}}
                @foreach ($elements as $element)
                    {{-- "Three Dots" Separator --}}
                    @if (is_string($element))
                        <li class="page-item disabled">
                            <span class="page-link dots">{{ $element }}</span>
                        </li>
                    @endif

                    {{-- Array Of Links --}}
                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            @if ($page == $paginator->currentPage())
                                <li class="page-item active">
                                    <span class="page-link current">{{ $page }}</span>
                                </li>
                            @else
                                <li class="page-item">
                                    <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                                </li>
                            @endif
                        @endforeach
                    @endif
                @endforeach

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <li class="page-item">
                        <a class="page-link prev-next" href="{{ $paginator->nextPageUrl() }}" rel="next">
                            <span class="d-none d-md-inline">ถัดไป</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link prev-next">
                            <span class="d-none d-md-inline">ถัดไป</span>
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                @endif
            </ul>
        </div>

        {{-- Results Info --}}
        <div class="pagination-info admin-info">
            <p class="text-muted small mb-0">
                <i class="fas fa-info-circle me-1"></i>
                แสดง {{ $paginator->firstItem() }} ถึง {{ $paginator->lastItem() }} 
                จากทั้งหมด {{ $paginator->total() }} รายการ
            </p>
        </div>
    </nav>

    <style>
    /* Admin-specific pagination styles */
    .admin-pagination {
        gap: 0.5rem;
    }

    .admin-pagination .page-link {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        color: #6c757d;
        font-weight: 400;
    }

    .admin-pagination .page-link:hover {
        background: #f8f9fa;
        border-color: #d0d0d0;
        color: #495057;
    }

    .admin-pagination .page-item.active .page-link {
        background: #007bff;
        border-color: #007bff;
        color: #ffffff;
        font-weight: 500;
    }

    .admin-pagination .page-link.prev-next {
        background: #ffffff;
        color: #6c757d;
        border-color: #e0e0e0;
        font-weight: 400;
    }

    .admin-pagination .page-link.prev-next:hover {
        background: #f8f9fa;
        border-color: #d0d0d0;
        color: #495057;
    }

    .admin-pagination .page-item.disabled .page-link.prev-next {
        background: #ffffff;
        color: #adb5bd;
        border-color: #e0e0e0;
    }

    .admin-info {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
    }

    .admin-info p {
        color: #6c757d !important;
        font-weight: 400;
    }

    /* Compact admin pagination for smaller screens */
    @media (max-width: 768px) {
        .admin-pagination {
            gap: 0.25rem;
        }

        .admin-pagination .page-link {
            min-width: 32px;
            height: 32px;
            padding: 0.25rem;
            font-size: 0.8rem;
        }
    }
    </style>
@endif
