<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ข้อมูลการตั้งค่าการอัปโหลด</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">ข้อมูลการตั้งค่าการอัปโหลดไฟล์</h4>
                    </div>
                    <div class="card-body">
                        <?php
                        $settings = [
                            'max_file_uploads' => ini_get('max_file_uploads'),
                            'upload_max_filesize' => ini_get('upload_max_filesize'),
                            'post_max_size' => ini_get('post_max_size'),
                            'max_execution_time' => ini_get('max_execution_time'),
                            'memory_limit' => ini_get('memory_limit')
                        ];
                        ?>
                        
                        <h5>การตั้งค่าปัจจุบัน</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>การตั้งค่า</th>
                                        <th>ค่าปัจจุบัน</th>
                                        <th>สถานะ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>จำนวนไฟล์สูงสุด</td>
                                        <td><?= $settings['max_file_uploads'] ?> ไฟล์</td>
                                        <td>
                                            <?php if ($settings['max_file_uploads'] >= 200): ?>
                                                <span class="badge bg-success">ไม่จำกัด</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">จำกัด</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>ขนาดไฟล์เดี่ยวสูงสุด</td>
                                        <td><?= $settings['upload_max_filesize'] ?></td>
                                        <td><span class="badge bg-info">ปกติ</span></td>
                                    </tr>
                                    <tr>
                                        <td>ขนาด POST สูงสุด</td>
                                        <td><?= $settings['post_max_size'] ?></td>
                                        <td><span class="badge bg-info">ปกติ</span></td>
                                    </tr>
                                    <tr>
                                        <td>เวลาประมวลผลสูงสุด</td>
                                        <td><?= $settings['max_execution_time'] ?> วินาที</td>
                                        <td><span class="badge bg-info">ปกติ</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <?php if ($settings['max_file_uploads'] < 200): ?>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> การตั้งค่ายังจำกัดจำนวนไฟล์</h6>
                            <p>ปัจจุบันระบบยังจำกัดการอัปโหลดที่ <?= $settings['max_file_uploads'] ?> ไฟล์ต่อครั้ง</p>
                            <p><strong>วิธีแก้ไข:</strong></p>
                            <ol>
                                <li>แก้ไขไฟล์ <code>php.ini</code> ใน XAMPP</li>
                                <li>เปลี่ยน <code>max_file_uploads = 200</code></li>
                                <li>Restart Apache ใน XAMPP Control Panel</li>
                            </ol>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> การตั้งค่าเหมาะสม</h6>
                            <p>ระบบพร้อมสำหรับการอัปโหลดไฟล์ไม่จำกัดจำนวนแล้ว!</p>
                        </div>
                        <?php endif; ?>

                        <h5>คำแนะนำการใช้งาน</h5>
                        <ul>
                            <li>สามารถอัปโหลดได้สูงสุด <strong><?= $settings['max_file_uploads'] ?> ไฟล์</strong> ต่อครั้ง</li>
                            <li>แต่ละไฟล์ขนาดไม่เกิน <strong><?= $settings['upload_max_filesize'] ?></strong></li>
                            <li>ขนาดรวมทั้งหมดไม่เกิน <strong><?= $settings['post_max_size'] ?></strong></li>
                            <li>หากอัปโหลดไฟล์จำนวนมาก ให้รอสักครู่เพื่อให้ระบบประมวลผล</li>
                        </ul>

                        <div class="mt-4">
                            <a href="/admin/images/create" class="btn btn-primary">
                                <i class="fas fa-upload"></i> ไปหน้าอัปโหลดรูปภาพ
                            </a>
                            <a href="/admin/news/create" class="btn btn-secondary">
                                <i class="fas fa-newspaper"></i> ไปหน้าสร้างข่าว
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
