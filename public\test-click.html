<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการคลิก</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/force-normal-colors.css" rel="stylesheet">
    <style>
        .test-button {
            margin: 10px;
            padding: 15px 30px;
            font-size: 18px;
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">ทดสอบการคลิกปุ่มและลิงก์</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>ปุ่ม Bootstrap</h3>
                <button class="btn btn-primary test-button" onclick="showResult('btn1')">ปุ่มหลัก</button>
                <button class="btn btn-success test-button" onclick="showResult('btn2')">ปุ่มสำเร็จ</button>
                <button class="btn btn-warning test-button" onclick="showResult('btn3')">ปุ่มเตือน</button>
                <button class="btn btn-danger test-button" onclick="showResult('btn4')">ปุ่มอันตราย</button>
            </div>
            
            <div class="col-md-6">
                <h3>ลิงก์</h3>
                <div class="d-flex flex-column">
                    <a href="#" class="btn btn-outline-primary test-button" onclick="showResult('link1'); return false;">ลิงก์ 1</a>
                    <a href="#" class="btn btn-outline-secondary test-button" onclick="showResult('link2'); return false;">ลิงก์ 2</a>
                    <a href="#" class="btn btn-outline-info test-button" onclick="showResult('link3'); return false;">ลิงก์ 3</a>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Navigation Menu Test</h3>
                <nav class="navbar navbar-expand-lg navbar-light bg-light">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#" onclick="showResult('nav-brand'); return false;">Brand</a>
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <ul class="navbar-nav">
                                <li class="nav-item">
                                    <a class="nav-link" href="#" onclick="showResult('nav1'); return false;">หน้าหลัก</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" onclick="showResult('nav2'); return false;">ข่าวสาร</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" onclick="showResult('nav3'); return false;">ติดต่อ</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div id="test-result" class="test-result success">
                    <strong>ผลการทดสอบ:</strong> <span id="result-text"></span>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>รูปภาพทดสอบ</h3>
                <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=Test+Image" 
                     class="img-fluid" 
                     alt="รูปทดสอบ"
                     onclick="showResult('image')"
                     style="cursor: pointer;">
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/force-normal-colors.js"></script>
    
    <script>
        function showResult(element) {
            const resultDiv = document.getElementById('test-result');
            const resultText = document.getElementById('result-text');
            
            resultText.textContent = `คลิก ${element} สำเร็จ! เวลา: ${new Date().toLocaleTimeString()}`;
            resultDiv.style.display = 'block';
            
            // ซ่อนผลลัพธ์หลังจาก 3 วินาที
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }
        
        // ทดสอบ console
        console.log('หน้าทดสอบโหลดเสร็จแล้ว');
        
        // ทดสอบ event listener
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM โหลดเสร็จแล้ว');
        });
    </script>
</body>
</html>
