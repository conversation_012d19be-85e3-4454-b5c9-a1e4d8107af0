<?php
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\News;
use App\Models\NewsImage;

echo "=== ตรวจสอบข่าว Project Approach เรื่องข้าว ===\n\n";

// ค้นหาข่าว Project Approach
$news = News::where('title', 'LIKE', '%Project Approach%')->with('images')->first();

if ($news) {
    echo "News ID: {$news->id}\n";
    echo "Title: {$news->title}\n";
    echo "Main Image: " . ($news->image ?? 'null') . "\n";
    
    if ($news->image) {
        $mainImagePath = public_path('storage/' . $news->image);
        echo "Main Image Exists: " . (file_exists($mainImagePath) ? 'YES' : 'NO') . "\n";
        echo "Main Image Full Path: {$mainImagePath}\n";
        echo "Main Image URL: " . asset('storage/' . $news->image) . "\n";
    }
    
    echo "Images Count: {$news->images->count()}\n";
    
    if ($news->images->count() > 0) {
        foreach ($news->images as $index => $image) {
            echo "\n--- Image {$index} ---\n";
            echo "ID: {$image->id}\n";
            echo "Original Path: {$image->image_path}\n";
            echo "Thumbnail Path: " . ($image->thumbnail_path ?? 'null') . "\n";
            
            // ทดสอบ path ต่างๆ
            $filename = basename($image->image_path);
            $paths = [
                $image->image_path,
                'news/' . $filename,
                str_replace('news/images/', 'news/', $image->image_path),
            ];
            
            echo "Testing paths:\n";
            foreach ($paths as $pathIndex => $path) {
                $fullPath = public_path('storage/' . $path);
                $exists = file_exists($fullPath);
                echo "  {$pathIndex}: {$path} -> " . ($exists ? 'EXISTS' : 'NOT FOUND') . "\n";
                if ($exists) {
                    echo "      URL: " . asset('storage/' . $path) . "\n";
                }
            }
            
            echo "Generated Image URL: {$image->image_url}\n";
            echo "Generated Thumbnail URL: {$image->thumbnail_url}\n";
        }
    }
} else {
    echo "ไม่พบข่าว Project Approach\n";
    
    // แสดงข่าวทั้งหมด
    echo "\nข่าวทั้งหมด:\n";
    $allNews = News::all();
    foreach ($allNews as $n) {
        echo "- ID: {$n->id}, Title: {$n->title}\n";
    }
}

echo "\n=== เสร็จสิ้น ===\n";
