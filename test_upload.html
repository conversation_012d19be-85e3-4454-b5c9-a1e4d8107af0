<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการอัปโหลดหลายรูป</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>ทดสอบการอัปโหลดรูปภาพหลายรูป</h2>
        
        <div class="alert alert-info">
            <h5>วิธีทดสอบ:</h5>
            <ol>
                <li>เข้าสู่ระบบด้วย email: <EMAIL> และ password: password</li>
                <li>ไปที่หน้า "จัดการรูปภาพ" ในระบบหลังบ้าน</li>
                <li>คลิก "อัปโหลดรูปภาพ"</li>
                <li>เลือก "อัปโหลดหลายรูป"</li>
                <li>เลือกรูปภาพหลายรูปพร้อมกัน</li>
                <li>กรอกข้อมูลและคลิก "อัปโหลด"</li>
            </ol>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>ลิงก์ที่สำคัญ</h5>
                    </div>
                    <div class="card-body">
                        <a href="http://127.0.0.1:8000" class="btn btn-primary mb-2 d-block">หน้าหลัก</a>
                        <a href="http://127.0.0.1:8000/login" class="btn btn-success mb-2 d-block">เข้าสู่ระบบ</a>
                        <a href="http://127.0.0.1:8000/admin" class="btn btn-info mb-2 d-block">ระบบหลังบ้าน</a>
                        <a href="http://127.0.0.1:8000/admin/images" class="btn btn-warning mb-2 d-block">จัดการรูปภาพ</a>
                        <a href="http://127.0.0.1:8000/admin/images/create" class="btn btn-danger mb-2 d-block">อัปโหลดรูปภาพ</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>ข้อมูลการเข้าสู่ระบบ</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Password:</strong> password</p>
                        <hr>
                        <h6>ฟีเจอร์ที่แก้ไขแล้ว:</h6>
                        <ul>
                            <li>✅ อัปโหลดรูปภาพหลายรูปพร้อมกัน (สูงสุด 10 รูป)</li>
                            <li>✅ ตรวจสอบขนาดไฟล์ (สูงสุด 5MB ต่อรูป)</li>
                            <li>✅ สร้าง thumbnail อัตโนมัติ</li>
                            <li>✅ ตั้งชื่อรูปอัตโนมัติ (เพิ่มหมายเลข)</li>
                            <li>✅ Preview รูปภาพก่อนอัปโหลด</li>
                            <li>✅ ลบรูปภาพจาก preview ได้</li>
                            <li>✅ แก้ไข error admin.news.edit ไม่พบ</li>
                            <li>✅ เพิ่มหน้าแก้ไขข่าว (edit.blade.php)</li>
                            <li>✅ เพิ่มหน้าดูรายละเอียดข่าว (show.blade.php)</li>
                            <li>✅ ล้าง cache และ routes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
