<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityImage;
use App\Models\ActivityCategory;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class ActivityImageController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ActivityImage::with('category')->latest();

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        $images = $query->paginate(12)->appends($request->query());
        $categories = ActivityCategory::withCount('images')->active()->ordered()->get();

        return view('admin.images.index', compact('images', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = ActivityCategory::active()->ordered()->get();
        return view('admin.images.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // ตรวจสอบโหมดการอัพโหลด
        $uploadMode = $request->input('upload_mode', 'single');

        if ($uploadMode === 'multiple') {
            return $this->storeMultiple($request);
        } else {
            return $this->storeSingle($request);
        }
    }

    /**
     * Store single image
     */
    private function storeSingle(Request $request)
    {
        $request->validate([
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:activity_categories,id',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
            'is_published' => 'boolean'
        ]);

        $data = $request->all();
        $data['is_published'] = $request->has('is_published');

        if ($request->hasFile('image')) {
            $imageData = $this->processImage($request->file('image'));
            $data['image_path'] = $imageData['image_path'];
            $data['thumbnail_path'] = $imageData['thumbnail_path'];
        }

        ActivityImage::create($data);

        return redirect()->route('admin.images.index')
            ->with('success', 'รูปภาพถูกอัพโหลดเรียบร้อยแล้ว');
    }

    /**
     * Store multiple images
     */
    private function storeMultiple(Request $request)
    {
        $request->validate([
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:activity_categories,id',
            'images' => 'required|array|min:1',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB per image
            'is_published' => 'boolean'
        ]);

        $baseData = [
            'description' => $request->description,
            'category_id' => $request->category_id,
            'is_published' => $request->has('is_published')
        ];

        $uploadedCount = 0;
        $errors = [];

        foreach ($request->file('images') as $index => $image) {
            try {
                $data = $baseData;

                // ประมวลผลรูปภาพ
                $imageData = $this->processImage($image);
                $data['image_path'] = $imageData['image_path'];
                $data['thumbnail_path'] = $imageData['thumbnail_path'];

                ActivityImage::create($data);
                $uploadedCount++;

            } catch (\Exception $e) {
                $errors[] = "รูปที่ " . ($index + 1) . ": " . $e->getMessage();
            }
        }

        $message = "อัพโหลดรูปภาพเรียบร้อย {$uploadedCount} รูป";
        if (!empty($errors)) {
            $message .= " (มีข้อผิดพลาด: " . implode(', ', $errors) . ")";
        }

        return redirect()->route('admin.images.index')
            ->with('success', $message)
            ->with('show_continue_upload', true)
            ->with('uploaded_count', $uploadedCount);
    }

    /**
     * Process single image (upload and create thumbnail)
     */
    private function processImage($image)
    {
        $filename = time() . '_' . uniqid() . '_' . \Str::slug(pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME)) . '.' . $image->getClientOriginalExtension();

        // เก็บรูปภาพในโฟลเดอร์ images
        $imagePath = $image->storeAs('images', $filename, 'public');

        // สร้าง thumbnail ในโฟลเดอร์ thumbnails
        $thumbnailFolder = 'images/thumbnails';
        $thumbnailPath = $thumbnailFolder . '/' . $filename;
        $thumbnailFullPath = storage_path('app/public/' . $thumbnailPath);

        // สร้างโฟลเดอร์ thumbnail หากยังไม่มี
        if (!file_exists(dirname($thumbnailFullPath))) {
            mkdir(dirname($thumbnailFullPath), 0755, true);
        }

        // สร้าง thumbnail (ใช้รูปต้นฉบับถ้า GD ไม่ทำงาน)
        if (extension_loaded('gd') && $this->createThumbnail(storage_path('app/public/' . $imagePath), $thumbnailFullPath, 300, 200)) {
            $thumbnailPath = $thumbnailPath;
        } else {
            // ถ้าสร้าง thumbnail ไม่ได้ ให้ใช้รูปต้นฉบับ
            $thumbnailPath = $imagePath;
        }

        // ซิงค์ไฟล์ไปยัง public/storage
        $this->syncImageToPublic($imagePath);
        if ($thumbnailPath !== $imagePath) {
            $this->syncImageToPublic($thumbnailPath);
        }

        return [
            'image_path' => $imagePath,
            'thumbnail_path' => $thumbnailPath
        ];
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $image = ActivityImage::findOrFail($id);
        return view('admin.images.show', compact('image'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $image = ActivityImage::findOrFail($id);
        $categories = ActivityCategory::active()->ordered()->get();
        return view('admin.images.edit', compact('image', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $image = ActivityImage::findOrFail($id);

        $request->validate([
            'description' => 'nullable|string',
            'category_id' => 'nullable|exists:activity_categories,id',
            'new_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'is_published' => 'boolean'
        ]);

        $data = $request->all();
        $data['is_published'] = $request->has('is_published');

        if ($request->hasFile('new_image')) {
            // ลบรูปเก่า
            if ($image->image_path) {
                Storage::disk('public')->delete($image->image_path);
            }
            if ($image->thumbnail_path) {
                Storage::disk('public')->delete($image->thumbnail_path);
            }

            $imageData = $this->processImage($request->file('new_image'));
            $data['image_path'] = $imageData['image_path'];
            $data['thumbnail_path'] = $imageData['thumbnail_path'];
        }

        $image->update($data);

        return redirect()->route('admin.images.index')
            ->with('success', 'รูปภาพถูกอัพเดทเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        \Log::info('ActivityImageController::destroy called with ID: ' . $id);

        try {
            $image = ActivityImage::findOrFail($id);
            \Log::info('Found image: ' . $image->id . ', path: ' . $image->image_path);

            // ลบรูปภาพจาก storage
            if ($image->image_path && Storage::disk('public')->exists($image->image_path)) {
                $deleted = Storage::disk('public')->delete($image->image_path);
                \Log::info('Deleted image file: ' . ($deleted ? 'success' : 'failed'));
            }

            if ($image->thumbnail_path && Storage::disk('public')->exists($image->thumbnail_path)) {
                $deleted = Storage::disk('public')->delete($image->thumbnail_path);
                \Log::info('Deleted thumbnail file: ' . ($deleted ? 'success' : 'failed'));
            }

            // ลบข้อมูลจากฐานข้อมูล
            $image->delete();
            \Log::info('Deleted image record from database');

            return redirect()->route('admin.images.index')
                ->with('success', 'รูปภาพถูกลบเรียบร้อยแล้ว');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Log::error('Image not found: ' . $id);
            return redirect()->route('admin.images.index')
                ->with('error', 'ไม่พบรูปภาพที่ต้องการลบ');
        } catch (\Exception $e) {
            \Log::error('Error deleting activity image: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return redirect()->route('admin.images.index')
                ->with('error', 'เกิดข้อผิดพลาดในการลบรูปภาพ: ' . $e->getMessage());
        }
    }

    /**
     * สร้าง thumbnail ด้วย GD library หรือ copy ถ้า GD ไม่ทำงาน
     */
    private function createThumbnail($sourcePath, $destinationPath, $width, $height)
    {
        // ตรวจสอบว่า GD extension ทำงานหรือไม่
        if (!extension_loaded('gd')) {
            // ถ้า GD ไม่ทำงาน ให้ copy ไฟล์ต้นฉบับ
            return copy($sourcePath, $destinationPath);
        }

        try {
            $imageInfo = getimagesize($sourcePath);
            if (!$imageInfo) {
                return copy($sourcePath, $destinationPath);
            }

            $mime = $imageInfo['mime'];

            switch ($mime) {
                case 'image/jpeg':
                    $source = imagecreatefromjpeg($sourcePath);
                    break;
                case 'image/png':
                    $source = imagecreatefrompng($sourcePath);
                    break;
                case 'image/gif':
                    $source = imagecreatefromgif($sourcePath);
                    break;
                default:
                    return copy($sourcePath, $destinationPath);
            }

            if (!$source) {
                return copy($sourcePath, $destinationPath);
            }

            $sourceWidth = imagesx($source);
            $sourceHeight = imagesy($source);

            // คำนวณขนาดใหม่โดยรักษาอัตราส่วน
            $ratio = min($width / $sourceWidth, $height / $sourceHeight);
            $newWidth = intval($sourceWidth * $ratio);
            $newHeight = intval($sourceHeight * $ratio);

            $thumbnail = imagecreatetruecolor($newWidth, $newHeight);

            // รักษาความโปร่งใสสำหรับ PNG
            if ($mime == 'image/png') {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefill($thumbnail, 0, 0, $transparent);
            }

            imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);

            // บันทึกไฟล์
            $result = false;
            switch ($mime) {
                case 'image/jpeg':
                    $result = imagejpeg($thumbnail, $destinationPath, 85);
                    break;
                case 'image/png':
                    $result = imagepng($thumbnail, $destinationPath);
                    break;
                case 'image/gif':
                    $result = imagegif($thumbnail, $destinationPath);
                    break;
            }

            imagedestroy($source);
            imagedestroy($thumbnail);

            return $result;

        } catch (\Exception $e) {
            // ถ้าเกิด error ให้ copy ไฟล์ต้นฉบับ
            return copy($sourcePath, $destinationPath);
        }
    }

    /**
     * Sync image file to public/storage directory
     */
    private function syncImageToPublic($imagePath)
    {
        try {
            $sourcePath = storage_path('app/public/' . $imagePath);
            $targetPath = public_path('storage/' . $imagePath);

            // สร้างโฟลเดอร์หากไม่มี
            $targetDir = dirname($targetPath);
            if (!file_exists($targetDir)) {
                mkdir($targetDir, 0755, true);
            }

            // คัดลอกไฟล์
            if (file_exists($sourcePath)) {
                copy($sourcePath, $targetPath);
            }
        } catch (\Exception $e) {
            // Log error but don't break the upload process
            \Log::warning('Failed to sync image to public: ' . $e->getMessage());
        }
    }

    /**
     * Toggle status of the specified resource.
     */
    public function toggleStatus($id)
    {
        try {
            $image = ActivityImage::findOrFail($id);
            $image->is_published = !$image->is_published;
            $image->save();

            return response()->json([
                'success' => true,
                'is_published' => $image->is_published,
                'message' => $image->is_published ? 'เผยแพร่รูปภาพเรียบร้อยแล้ว' : 'เปลี่ยนเป็นร่างเรียบร้อยแล้ว'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการอัพเดทสถานะ'
            ], 500);
        }
    }
}
