<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class News extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'is_published'
    ];

    protected $casts = [
        'is_published' => 'boolean'
    ];

    public function images()
    {
        return $this->hasMany(NewsImage::class)->ordered();
    }

    public function featuredImage()
    {
        return $this->hasOne(NewsImage::class)->where('is_featured', true);
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    // Accessor สำหรับรูปภาพหลัก
    public function getMainImageAttribute()
    {
        // ถ้ามี featured image ให้ใช้ featured image
        if ($this->featuredImage) {
            return $this->featuredImage->image_path;
        }

        // ถ้ามีรูปภาพใน images ให้ใช้รูปแรก
        if ($this->images->count() > 0) {
            return $this->images->first()->image_path;
        }

        // ถ้าไม่มีรูปภาพเลย
        return null;
    }
}
