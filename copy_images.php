<?php

function copyDirectory($source, $dest) {
    if (!is_dir($dest)) {
        mkdir($dest, 0755, true);
    }
    
    $files = scandir($source);
    $count = 0;
    
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $sourcePath = $source . DIRECTORY_SEPARATOR . $file;
            $destPath = $dest . DIRECTORY_SEPARATOR . $file;
            
            if (is_file($sourcePath)) {
                if (copy($sourcePath, $destPath)) {
                    echo "Copied: {$file}\n";
                    $count++;
                } else {
                    echo "Failed to copy: {$file}\n";
                }
            }
        }
    }
    
    return $count;
}

echo "=== Copy รูปภาพข่าว ===\n\n";

// Copy images
$sourceImages = 'storage' . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'news' . DIRECTORY_SEPARATOR . 'images';
$destImages = 'public' . DIRECTORY_SEPARATOR . 'storage' . DIRECTORY_SEPARATOR . 'news' . DIRECTORY_SEPARATOR . 'images';

if (is_dir($sourceImages)) {
    echo "Copying images from {$sourceImages} to {$destImages}\n";
    $count = copyDirectory($sourceImages, $destImages);
    echo "Copied {$count} image files\n\n";
} else {
    echo "Source images directory not found: {$sourceImages}\n\n";
}

// Copy thumbnails
$sourceThumbnails = 'storage' . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'news' . DIRECTORY_SEPARATOR . 'thumbnails';
$destThumbnails = 'public' . DIRECTORY_SEPARATOR . 'storage' . DIRECTORY_SEPARATOR . 'news' . DIRECTORY_SEPARATOR . 'thumbnails';

if (is_dir($sourceThumbnails)) {
    echo "Copying thumbnails from {$sourceThumbnails} to {$destThumbnails}\n";
    $count = copyDirectory($sourceThumbnails, $destThumbnails);
    echo "Copied {$count} thumbnail files\n\n";
} else {
    echo "Source thumbnails directory not found: {$sourceThumbnails}\n\n";
}

echo "=== เสร็จสิ้น ===\n";
