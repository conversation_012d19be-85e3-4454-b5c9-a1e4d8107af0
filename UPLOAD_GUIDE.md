# 📸 คู่มือการใช้งานระบบอัพโหลดรูปภาพ

## 🔐 การเข้าสู่ระบบ

### ข้อมูลการเข้าสู่ระบบ
- **URL**: http://localhost:8000/login
- **Email**: <EMAIL>
- **Password**: password

## 📁 โครงสร้างโฟลเดอร์ใหม่

ระบบจะสร้างโฟลเดอร์แยกตามหมวดหมู่อัตโนมัติ:

```
storage/app/public/activity_images/
├── กิจกรรมการเรียนรู/
│   ├── รูปภาพต้นฉบับ
│   └── thumbnails/
│       └── รูป thumbnail
├── กิจกรรมกีฬา/
│   ├── รูปภาพต้นฉบับ
│   └── thumbnails/
├── กิจกรรมศิลปะ/
│   ├── รูปภาพต้นฉบับ
│   └── thumbnails/
└── กิจกรรมพิเศษ/
    ├── รูปภาพต้นฉบับ
    └── thumbnails/
```

## 🖼️ การอัพโหลดรูปภาพ

### ขั้นตอนการอัพโหลด
1. **เข้าสู่ระบบ** → Admin Dashboard
2. **คลิก** "จัดการรูปภาพ" หรือ "อัพโหลดรูปภาพ"
3. **กรอกข้อมูล**:
   - ชื่อรูปภาพ (บังคับ)
   - คำอธิบาย (ไม่บังคับ)
   - หมวดหมู่ (บังคับ)
   - วันที่กิจกรรม (บังคับ)
   - ไฟล์รูปภาพ (บังคับ)
   - ลำดับการแสดง (ไม่บังคับ)
   - เผยแพร่ทันที (ไม่บังคับ)

### ข้อกำหนดไฟล์รูปภาพ
- **ประเภทไฟล์**: JPG, PNG, GIF
- **ขนาดไฟล์**: ไม่เกิน 5MB
- **ขนาดภาพ**: แนะนำ 800x600 พิกเซลขึ้นไป

## 🗂️ หมวดหมู่ที่มีในระบบ

1. **กิจกรรมการเรียนรู้** - รูปภาพกิจกรรมการเรียนการสอน
2. **กิจกรรมกีฬา** - รูปภาพกิจกรรมกีฬาและการออกกำลังกาย
3. **กิจกรรมศิลปะ** - รูปภาพกิจกรรมศิลปะและงานฝีมือ
4. **กิจกรรมพิเศษ** - รูปภาพกิจกรรมพิเศษและงานเทศกาล

## 🔧 คุณสมบัติของระบบ

### ✅ ที่ทำงานได้แล้ว
- ✅ อัพโหลดรูปภาพ
- ✅ สร้างโฟลเดอร์แยกตามหมวดหมู่
- ✅ ตั้งชื่อไฟล์อัตโนมัติ (timestamp + ชื่อไฟล์)
- ✅ จัดการข้อมูลรูปภาพ (CRUD)
- ✅ ระบบ validation
- ✅ แสดงรูปภาพในเว็บไซต์
- ✅ ระบบ pagination
- ✅ ระบบสิทธิ์การเข้าถึง

### ⚠️ ข้อจำกัดปัจจุบัน
- ⚠️ GD Extension ไม่ทำงาน (ใช้รูปต้นฉบับแทน thumbnail)
- ⚠️ ต้องเปิดใช้งาน GD Extension ใน php.ini สำหรับ thumbnail

## 🌐 การแสดงผลในเว็บไซต์

### หน้าแกลเลอรี่
- **URL**: http://localhost:8000/gallery
- แสดงรูปภาพทั้งหมดที่เผยแพร่แล้ว
- แยกตามหมวดหมู่
- มี pagination

### หน้าหมวดหมู่
- **URL**: http://localhost:8000/gallery/category/{id}
- แสดงรูปภาพในหมวดหมู่ที่เลือก
- มี modal สำหรับดูรูปขนาดใหญ่

## 🔍 การทดสอบระบบ

### ทดสอบการอัพโหลด
1. เข้าสู่ระบบ Admin
2. ไปที่ "จัดการรูปภาพ"
3. คลิก "อัพโหลดรูปภาพ"
4. กรอกข้อมูลและเลือกไฟล์
5. คลิก "อัพโหลด"

### ตรวจสอบผลลัพธ์
1. ตรวจสอบในหน้า "จัดการรูปภาพ"
2. ตรวจสอบโฟลเดอร์ `storage/app/public/activity_images/`
3. ตรวจสอบการแสดงผลในเว็บไซต์

## 🛠️ การแก้ไขปัญหา

### ปัญหาที่อาจพบ
1. **ไม่สามารถอัพโหลดได้**
   - ตรวจสอบสิทธิ์โฟลเดอร์ storage
   - ตรวจสอบขนาดไฟล์
   - ตรวจสอบประเภทไฟล์

2. **รูปภาพไม่แสดง**
   - ตรวจสอบ symlink: `php artisan storage:link`
   - ตรวจสอบ URL รูปภาพ

3. **Thumbnail ไม่สร้าง**
   - เปิดใช้งาน GD Extension ใน php.ini
   - Restart Apache/Nginx

## 📞 การติดต่อ

หากพบปัญหาการใช้งาน กรุณาติดต่อผู้ดูแลระบบ
