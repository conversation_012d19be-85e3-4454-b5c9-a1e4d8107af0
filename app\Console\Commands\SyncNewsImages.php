<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NewsImage;
use Illuminate\Support\Facades\File;

class SyncNewsImages extends Command
{
    protected $signature = 'news:sync-images';
    protected $description = 'Sync news images from storage/app/public to public/storage';

    public function handle()
    {
        $this->info('Starting to sync news images...');

        $newsImages = NewsImage::all();
        $synced = 0;
        $errors = 0;

        foreach ($newsImages as $image) {
            $this->info("Processing image ID: {$image->id}");

            // Sync รูปต้นฉบับ
            if ($this->syncFile($image->image_path)) {
                $this->info("  - ✅ Synced image: {$image->image_path}");
                $synced++;
            } else {
                $this->error("  - ❌ Failed to sync image: {$image->image_path}");
                $errors++;
            }

            // Sync thumbnail (ถ้าแตกต่างจากรูปต้นฉบับ)
            if ($image->thumbnail_path && $image->thumbnail_path !== $image->image_path) {
                if ($this->syncFile($image->thumbnail_path)) {
                    $this->info("  - ✅ Synced thumbnail: {$image->thumbnail_path}");
                    $synced++;
                } else {
                    $this->error("  - ❌ Failed to sync thumbnail: {$image->thumbnail_path}");
                    $errors++;
                }
            }
        }

        $this->info("\n=== Summary ===");
        $this->info("Total images processed: " . $newsImages->count());
        $this->info("Files synced: {$synced}");
        $this->info("Errors: {$errors}");

        if ($synced > 0) {
            $this->info("\n✅ Image sync completed successfully!");
        } else {
            $this->warn("\n⚠️  No files were synced.");
        }

        return 0;
    }

    /**
     * ซิงค์ไฟล์จาก storage/app/public ไปยัง public/storage
     */
    private function syncFile($imagePath)
    {
        if (!$imagePath) {
            return false;
        }

        $sourcePath = storage_path('app/public/' . str_replace('\\', '/', $imagePath));
        $targetPath = public_path('storage/' . str_replace('\\', '/', $imagePath));

        // ตรวจสอบว่าไฟล์ต้นฉบับมีอยู่หรือไม่
        if (!file_exists($sourcePath)) {
            $this->warn("    Source file not found: {$sourcePath}");
            return false;
        }

        // ตรวจสอบว่าไฟล์ปลายทางมีอยู่แล้วหรือไม่
        if (file_exists($targetPath)) {
            $this->info("    Target file already exists, skipping: {$targetPath}");
            return true;
        }

        // สร้างโฟลเดอร์ target หากไม่มี
        $targetDir = dirname($targetPath);
        if (!file_exists($targetDir)) {
            if (!mkdir($targetDir, 0755, true)) {
                $this->error("    Failed to create directory: {$targetDir}");
                return false;
            }
        }

        // คัดลอกไฟล์
        if (copy($sourcePath, $targetPath)) {
            $this->info("    Copied: {$sourcePath} -> {$targetPath}");
            return true;
        } else {
            $this->error("    Failed to copy file");
            return false;
        }
    }
}
