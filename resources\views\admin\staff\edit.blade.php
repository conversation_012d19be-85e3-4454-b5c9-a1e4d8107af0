@extends('layouts.admin')

@section('title', 'แก้ไขข้อมูลบุคลากร - ระบบจัดการ')
@section('page-title', 'แก้ไขข้อมูลบุคลากร')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">แก้ไขข้อมูลบุคลากร</h4>
    <a href="{{ route('admin.staff.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>กลับ
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อมูลบุคลากร
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.staff.update', $staff->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">ชื่อ-นามสกุล <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name', $staff->name) }}" 
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="position" class="form-label">ตำแหน่ง <span class="text-danger">*</span></label>
                                <input type="text"
                                       class="form-control @error('position') is-invalid @enderror"
                                       id="position"
                                       name="position"
                                       value="{{ old('position', $staff->position) }}"
                                       placeholder="ระบุตำแหน่ง เช่น ผู้อำนวยการ, ครูผู้ช่วย, ครูผู้ดูแลเด็ก"
                                       required>
                                @error('position')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">กรอกตำแหน่งงานของบุคลากร</div>
                            </div>
                        </div>
                    </div>
                    

                    

                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">สถานะ</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           value="1"
                                           {{ old('is_active', $staff->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        เปิดใช้งาน
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="photo" class="form-label">รูปภาพ</label>
                        <input type="file" 
                               class="form-control @error('photo') is-invalid @enderror" 
                               id="photo" 
                               name="photo" 
                               accept="image/*">
                        @error('photo')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.staff.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>ยกเลิก
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-image me-2"></i>รูปภาพปัจจุบัน
                </h5>
            </div>
            <div class="card-body text-center">
                @if($staff->photo)
                    <img src="{{ asset('storage/' . $staff->photo) }}" 
                         alt="{{ $staff->name }}"
                         class="img-fluid rounded mb-3"
                         style="max-width: 200px; max-height: 200px; object-fit: cover;">
                    <p class="text-muted">รูปภาพปัจจุบัน</p>
                @else
                    <div class="bg-light rounded d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 200px; height: 200px;">
                        <i class="fas fa-user fa-5x text-muted"></i>
                    </div>
                    <p class="text-muted">ยังไม่มีรูปภาพ</p>
                @endif
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>คำแนะนำ
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>ใส่ข้อมูลที่จำเป็นให้ครบถ้วน</li>
                    <li><i class="fas fa-check text-success me-2"></i>รูปภาพควรมีขนาดเหมาะสม</li>
                    <li><i class="fas fa-check text-success me-2"></i>ตรวจสอบข้อมูลก่อนบันทึก</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// ไม่ต้องมี JavaScript สำหรับตำแหน่งแล้ว เนื่องจากเป็นช่องกรอกข้อมูลธรรมดา
</script>
@endsection
