@extends('layouts.app')

@section('title', 'รูปภาพกิจกรรม - ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า')

@section('styles')
<style>
    .gallery-item {
        position: relative;
        overflow: hidden;
        border-radius: 10px;
        cursor: pointer;
    }

    .gallery-item img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        object-position: center center;
    }

    .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
        opacity: 0;
        display: flex;
        align-items: flex-end;
        padding: 20px;
    }

    .gallery-info {
        color: white;
        width: 100%;
    }

    .category-filter {
        background: white;
        border-radius: 50px;
        padding: 10px 20px;
        margin: 5px;
        border: 2px solid #e9ecef;
    }

    .category-filter.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
</style>
@endsection

@section('content')
<!-- Page Header -->
<section class="bg-info text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-images me-3"></i>
                    รูปภาพกิจกรรม
                </h1>
                <p class="lead">ชมภาพบรรยากาศกิจกรรมต่างๆ ของศูนย์พัฒนาเด็กเล็ก</p>
            </div>
        </div>

        <!-- Category Filter -->
        @if($categories->count() > 0)
        <div class="row justify-content-center mt-4">
            <div class="col-lg-10">
                <div class="card shadow-sm border-0" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                    <div class="card-body py-4">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <h6 class="mb-0 text-primary fw-bold">
                                    <i class="fas fa-filter me-2"></i>หมวดหมู่:
                                </h6>
                            </div>
                            <div class="col-md-10">
                                <div class="d-flex flex-wrap gap-3">
                                    <a href="{{ route('gallery.index') }}"
                                       class="btn {{ !request('category_id') ? 'btn-primary' : 'btn-outline-primary' }} rounded-pill px-4 py-2 shadow-sm">
                                        <i class="fas fa-th-large me-2"></i>ทั้งหมด
                                        <span class="badge {{ !request('category_id') ? 'bg-light text-primary' : 'bg-primary text-white' }} ms-2">{{ $totalImages ?? 0 }}</span>
                                    </a>
                                    @foreach($categories as $category)
                                        <a href="{{ route('gallery.category', $category->slug) }}"
                                           class="btn {{ request('category_id') == $category->id ? 'btn-primary' : 'btn-outline-primary' }} rounded-pill px-4 py-2 shadow-sm">
                                            <i class="fas fa-images me-2"></i>{{ $category->name }}
                                            <span class="badge {{ request('category_id') == $category->id ? 'bg-light text-primary' : 'bg-primary text-white' }} ms-2">{{ $category->images_count }}</span>
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Gallery Content -->
<section class="py-5">
    <div class="container">
        @if($images->count() > 0)
            <div class="row g-4">
                @foreach($images as $image)
                <div class="col-lg-4 col-md-6">
                    <div class="gallery-item" data-bs-toggle="modal" data-bs-target="#imageModal{{ $image->id }}">
                        <img src="{{ $image->thumbnail_url ?? $image->image_url }}"
                             alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                             class="img-fluid">
                        
                        <div class="gallery-overlay">
                            <div class="gallery-info">
                                <h6 class="mb-1">รูปภาพกิจกรรม</h6>
                                @if($image->description)
                                    <p class="mb-1 small">{{ Str::limit($image->description, 60) }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Image Modal -->
                <div class="modal fade" id="imageModal{{ $image->id }}" tabindex="-1">
                    <div class="modal-dialog modal-lg modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">รูปภาพกิจกรรม</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="{{ $image->image_url }}"
                                     alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                                     class="img-fluid rounded" style="max-height: 70vh;">

                                @if($image->description)
                                    <div class="mt-3">
                                        <p class="text-muted">{{ $image->description }}</p>
                                    </div>
                                @endif
                            </div>
                            <div class="modal-footer justify-content-end">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if(method_exists($images, 'links'))
            <div class="d-flex justify-content-center mt-5">
                {{ $images->links('custom.pagination') }}
            </div>
            @endif
        @else
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-5x text-muted mb-4"></i>
                        <h3 class="text-muted">ยังไม่มีรูปภาพกิจกรรม</h3>
                        <p class="text-muted">กรุณาติดตามรูปภาพกิจกรรมในภายหลัง</p>
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>กลับหน้าหลัก
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>


@endsection
