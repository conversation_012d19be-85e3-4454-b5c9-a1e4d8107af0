<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="">
    <title>ทดสอบการลบข้อมูล</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>ทดสอบการลบข้อมูล</h2>
        
        <div class="alert alert-info">
            <strong>คำแนะนำ:</strong> 
            <ol>
                <li>เข้าสู่ระบบที่ <a href="http://127.0.0.1:8000/login" target="_blank">http://127.0.0.1:8000/login</a></li>
                <li>ใช้ Email: <EMAIL>, Password: password</li>
                <li>ไปที่หน้า <a href="http://127.0.0.1:8000/admin/news" target="_blank">จัดการข่าวสาร</a></li>
                <li>ลองกดปุ่มลบข่าว "ข่าวทดสอบการลบผ่านเว็บ"</li>
                <li>เปิด Developer Tools (F12) เพื่อดู Console logs</li>
            </ol>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>ข้อมูลทดสอบ</h5>
            </div>
            <div class="card-body">
                <p><strong>ข่าวทดสอบ ID:</strong> 14</p>
                <p><strong>ชื่อข่าว:</strong> ข่าวทดสอบการลบผ่านเว็บ</p>
                
                <button class="btn btn-primary" onclick="testDeleteRequest()">ทดสอบ DELETE Request</button>
                <button class="btn btn-secondary" onclick="checkLogs()">ตรวจสอบ Logs</button>
            </div>
        </div>

        <div id="result" class="mt-3"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testDeleteRequest() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info">กำลังทดสอบ DELETE request...</div>';
            
            // ทดสอบส่ง DELETE request
            fetch('/admin/news/14', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (response.redirected) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <strong>✓ Request สำเร็จ!</strong><br>
                            Status: ${response.status}<br>
                            Redirected to: ${response.url}
                        </div>
                    `;
                } else {
                    return response.text().then(text => {
                        resultDiv.innerHTML = `
                            <div class="alert alert-warning">
                                <strong>Response ได้รับ:</strong><br>
                                Status: ${response.status}<br>
                                <pre>${text}</pre>
                            </div>
                        `;
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ เกิดข้อผิดพลาด:</strong><br>
                        ${error.message}
                    </div>
                `;
            });
        }

        function checkLogs() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="alert alert-info">
                    <strong>วิธีตรวจสอบ Logs:</strong><br>
                    1. เปิด Terminal/Command Prompt<br>
                    2. ไปที่โฟลเดอร์โปรเจค<br>
                    3. รันคำสั่ง: <code>tail -f storage/logs/laravel.log</code><br>
                    หรือดูไฟล์ <code>storage/logs/laravel.log</code> โดยตรง
                </div>
            `;
        }

        // ดึง CSRF token จากหน้า login
        fetch('/login')
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const csrfToken = doc.querySelector('meta[name="csrf-token"]');
                if (csrfToken) {
                    document.querySelector('meta[name="csrf-token"]').setAttribute('content', csrfToken.getAttribute('content'));
                    console.log('CSRF token loaded:', csrfToken.getAttribute('content'));
                }
            })
            .catch(error => {
                console.error('Error loading CSRF token:', error);
            });
    </script>
</body>
</html>
