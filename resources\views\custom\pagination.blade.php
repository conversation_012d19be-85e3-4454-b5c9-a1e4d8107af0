@if ($paginator->hasPages())
    <nav aria-label="Pagination Navigation" class="pagination-wrapper">
        <div class="pagination-container">
            <ul class="pagination modern-pagination">
                {{-- Previous Page Link --}}
                @if ($paginator->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link prev-next">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-sm-inline">ก่อนหน้า</span>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link prev-next" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                            <i class="fas fa-chevron-left"></i>
                            <span class="d-none d-sm-inline">ก่อนหน้า</span>
                        </a>
                    </li>
                @endif

                {{-- Pagination Elements --}}
                @foreach ($elements as $element)
                    {{-- "Three Dots" Separator --}}
                    @if (is_string($element))
                        <li class="page-item disabled">
                            <span class="page-link dots">{{ $element }}</span>
                        </li>
                    @endif

                    {{-- Array Of Links --}}
                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            @if ($page == $paginator->currentPage())
                                <li class="page-item active">
                                    <span class="page-link current">{{ $page }}</span>
                                </li>
                            @else
                                <li class="page-item">
                                    <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                                </li>
                            @endif
                        @endforeach
                    @endif
                @endforeach

                {{-- Next Page Link --}}
                @if ($paginator->hasMorePages())
                    <li class="page-item">
                        <a class="page-link prev-next" href="{{ $paginator->nextPageUrl() }}" rel="next">
                            <span class="d-none d-sm-inline">ถัดไป</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link prev-next">
                            <span class="d-none d-sm-inline">ถัดไป</span>
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                @endif
            </ul>
        </div>

        {{-- Results Info --}}
        <div class="pagination-info">
            <p class="text-muted small mb-0">
                แสดง {{ $paginator->firstItem() }} ถึง {{ $paginator->lastItem() }} 
                จากทั้งหมด {{ $paginator->total() }} รายการ
            </p>
        </div>
    </nav>

    <style>
    .pagination-wrapper {
        margin: 2rem 0;
        text-align: center;
    }

    .pagination-container {
        display: flex;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .modern-pagination {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .modern-pagination .page-item {
        margin: 0;
    }

    .modern-pagination .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        padding: 0.5rem 0.75rem;
        margin: 0;
        font-size: 0.875rem;
        font-weight: 400;
        color: #6c757d;
        text-decoration: none;
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        transition: all 0.15s ease;
    }

    .modern-pagination .page-link:hover {
        color: #495057;
        background: #f8f9fa;
        border-color: #d0d0d0;
    }

    .modern-pagination .page-item.active .page-link {
        color: #ffffff;
        background: #007bff;
        border-color: #007bff;
        font-weight: 500;
    }

    .modern-pagination .page-item.disabled .page-link {
        color: #adb5bd;
        background: #ffffff;
        border-color: #e0e0e0;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .modern-pagination .page-link.prev-next {
        min-width: auto;
        padding: 0.5rem 0.875rem;
        gap: 0.375rem;
        font-weight: 400;
        color: #6c757d;
        background: #ffffff;
        border-color: #e0e0e0;
    }

    .modern-pagination .page-link.prev-next:hover {
        color: #495057;
        background: #f8f9fa;
        border-color: #d0d0d0;
    }

    .modern-pagination .page-item.disabled .page-link.prev-next {
        color: #adb5bd;
        background: #ffffff;
        border-color: #e0e0e0;
    }

    .modern-pagination .page-link.dots {
        background: transparent;
        border: none;
        cursor: default;
        font-weight: 400;
        color: #6c757d;
    }

    .pagination-info {
        margin-top: 1rem;
        padding: 0.5rem 1rem;
        background: #f8f9fa;
        border-radius: 4px;
        display: inline-block;
        border: 1px solid #e0e0e0;
    }

    .pagination-info p {
        margin: 0;
        font-weight: 400;
        color: #6c757d;
        font-size: 0.875rem;
    }

    /* Responsive Design */
    @media (max-width: 576px) {
        .modern-pagination {
            gap: 0.25rem;
        }

        .modern-pagination .page-link {
            min-width: 32px;
            height: 32px;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .modern-pagination .page-link.prev-next {
            padding: 0.25rem 0.625rem;
        }

        .pagination-info {
            padding: 0.375rem 0.75rem;
            font-size: 0.8rem;
        }
    }
    </style>
@endif
