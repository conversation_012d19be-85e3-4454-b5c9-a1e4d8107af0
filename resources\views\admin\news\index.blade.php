@extends('layouts.admin')

@section('title', 'จัดการข่าวสาร - ระบบจัดการ')
@section('page-title', 'จัดการข่าวสาร')

@section('content')
<!-- แสดงข้อความแจ้งเตือน -->
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">รายการข่าวสาร</h4>
    <div>
        <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>เพิ่มข่าวสาร
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        @if($news->count() > 0)
            <div class="table-responsive">
                <table class="table data-table">
                    <thead>
                        <tr>
                            <th style="width: 80px;">รูปภาพ</th>
                            <th>หัวข้อ</th>
                            <th style="width: 150px;">การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($news as $newsItem)
                        <tr>
                            <td>
                                @php
                                    $thumbnailUrl = null;

                                    // ลำดับความสำคัญ: 1. Featured image 2. รูปแรกใน collection
                                    if ($newsItem->featuredImage) {
                                        $thumbnailUrl = $newsItem->featuredImage->thumbnail_url ?: $newsItem->featuredImage->image_url;
                                    } elseif ($newsItem->images && $newsItem->images->count() > 0) {
                                        $thumbnailUrl = $newsItem->images->first()->thumbnail_url ?: $newsItem->images->first()->image_url;
                                    }
                                @endphp

                                @if($thumbnailUrl)
                                    <img src="{{ $thumbnailUrl }}"
                                         alt="{{ $newsItem->title }}"
                                         class="img-thumbnail"
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                @else
                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                         style="width: 60px; height: 60px; border-radius: 5px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <strong>{{ $newsItem->title }}</strong>
                                <br>
                                <small class="text-muted">
                                    {{ Str::limit(strip_tags($newsItem->content), 80) }}
                                </small>
                                @if($newsItem->images && $newsItem->images->count() > 0)
                                    <br>
                                    <small class="text-info">
                                        <i class="fas fa-images me-1"></i>
                                        {{ $newsItem->images->count() }} รูปภาพ
                                    </small>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.news.edit', $newsItem->id) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.news.images.manage', $newsItem->id) }}"
                                       class="btn btn-sm btn-outline-success"
                                       title="จัดการรูปภาพ">
                                        <i class="fas fa-images"></i>
                                    </a>
                                    <form action="{{ route('admin.news.destroy', $newsItem->id) }}"
                                          method="POST"
                                          style="display: inline;"
                                          onsubmit="return confirm('คุณต้องการลบข่าว {{ $newsItem->title }} หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="btn btn-sm btn-outline-danger"
                                                title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $news->links('custom.admin-pagination') }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-5x text-muted mb-4"></i>
                <h5 class="text-muted">ยังไม่มีข่าวสาร</h5>
                <p class="text-muted">เริ่มต้นสร้างข่าวสารแรกของคุณ</p>
                <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มข่าวสาร
                </a>
            </div>
        @endif
    </div>
</div>


@endsection

@section('styles')
<style>
    /* ========== ปรับสี Badge ในตาราง ========== */
    .table .badge.bg-success {
        background-color: #28a745 !important;
        color: #ffffff !important;
        font-weight: 600;
        padding: 8px 12px;
        border-radius: 20px;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        border: 2px solid #ffffff;
    }

    .table .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #212529 !important;
        font-weight: 600;
        padding: 8px 12px;
        border-radius: 20px;
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        border: 2px solid #ffffff;
    }

    /* ========== ปรับสีข้อความในตาราง ========== */
    .table th {
        background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
        color: #1a252f !important;
        font-weight: 700;
        text-align: center;
        border: 2px solid #3498db;
        padding: 15px 10px;
        text-shadow: none;
        font-size: 0.95rem;
    }

    .table td {
        vertical-align: middle;
        padding: 12px 10px;
        border-bottom: 1px solid #e9ecef;
        color: #2c3e50 !important;
        font-weight: 500;
    }

    /* Table row hover effects removed */

    /* ========== ปรับสีข้อความเฉพาะในตาราง ========== */
    .table td strong {
        color: #1a252f !important;
        font-weight: 600;
    }

    .table td .text-muted {
        color: #6c757d !important;
    }

    .table td .fw-medium {
        color: #2c3e50 !important;
        font-weight: 600;
    }

    /* ========== ปรับสีปุ่มในตาราง ========== */
    .table .btn-outline-primary {
        border-color: #3498db;
        color: #3498db;
        font-weight: 500;
    }

    .table .btn-outline-primary:hover {
        background-color: #3498db;
        border-color: #3498db;
        color: #ffffff;
    }

    .table .btn-outline-danger {
        border-color: #e74c3c;
        color: #e74c3c;
        font-weight: 500;
    }

    .table .btn-outline-danger:hover {
        background-color: #e74c3c;
        border-color: #e74c3c;
        color: #ffffff;
    }

    /* ========== Toggle Switch Styling ========== */
    .form-check-input:checked {
        background-color: #00d084 !important;
        border-color: #00d084 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 208, 132, 0.25) !important;
    }

    .form-check-input:focus {
        border-color: #80bdff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    .form-check-input {
        width: 3.5rem !important;
        height: 1.8rem !important;
        border-radius: 1rem !important;
        background-color: #dee2e6 !important;
        border: 2px solid #dee2e6 !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
    }

    .form-check-input:checked::before {
        transform: translateX(1.7rem) !important;
    }

    .status-text {
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        margin-left: 0.5rem !important;
    }

    .form-check {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin-bottom: 0 !important;
    }

    .table td {
        vertical-align: middle !important;
    }
</style>
@endsection

@section('scripts')
@endsection
