<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Staff extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'position',
        'photo',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('name');
    }

    // Accessor สำหรับ URL รูปภาพ
    public function getPhotoUrlAttribute()
    {
        if (!$this->photo) {
            return null;
        }

        // ใช้ cache เพื่อป้องกันการเรียกซ้ำ
        static $cache = [];
        $cacheKey = 'photo_url_' . $this->id . '_' . md5($this->photo);

        if (isset($cache[$cacheKey])) {
            return $cache[$cacheKey];
        }

        // แปลง backslash เป็น forward slash สำหรับ Windows
        $normalizedPath = str_replace('\\', '/', $this->photo);

        // ดึงชื่อไฟล์จาก path
        $filename = basename($normalizedPath);

        // ลำดับความสำคัญของ path ที่จะลอง
        $possiblePaths = [
            'staff/' . $filename,    // ไฟล์ในโฟลเดอร์ staff/
            $normalizedPath,         // path ที่เก็บไว้ในฐานข้อมูล
        ];

        // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
        foreach ($possiblePaths as $path) {
            $fullPath = public_path('storage/' . $path);
            if (file_exists($fullPath)) {
                $result = asset('storage/' . $path);
                $cache[$cacheKey] = $result;
                return $result;
            }
        }

        // ถ้าไม่พบไฟล์ ให้ return null
        $cache[$cacheKey] = null;
        return null;
    }
}
