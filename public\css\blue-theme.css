/* ========================================
   ธีมสีเด็ก สำหรับระบบหลังบ้าน ศูนย์พัฒนาเด็กเล็ก
   Child-Friendly Theme for Child Care Center Admin
   ======================================== */

/* ========== CSS Variables - Navy Blue Theme ========== */
:root {
    /* สีหลัก - ธีมฟ้าคราม */
    --admin-primary: #3498DB !important;        /* สีฟ้าหลัก (เหมือนเว็บไซต์หลัก) */
    --admin-secondary: #74B9FF !important;      /* สีฟ้าสดใส */
    --admin-accent: #A8E6CF !important;         /* สีเขียวมิ้นท์อ่อน */
    --admin-light: #E8F4FD !important;          /* สีฟ้าอ่อนมาก */
    --admin-lighter: #F8FBFF !important;        /* สีขาวฟ้า */

    /* สีฟ้าคราม สำหรับ Sidebar */
    --navy-primary: #1e3a8a !important;         /* ฟ้าคราม */
    --navy-secondary: #1e40af !important;       /* ฟ้าคราม กลาง */
    --navy-light: #2563eb !important;           /* ฟ้าคราม อ่อน */

    /* สีเสริมสำหรับเด็ก */
    --child-pink: #FFB3BA !important;           /* ชมพูอ่อน */
    --child-yellow: #FFDFBA !important;         /* เหลืองอ่อน */
    --child-green: #BAFFC9 !important;          /* เขียวอ่อน */
    --child-purple: #D4BAFF !important;         /* ม่วงอ่อน */

    /* สีสำหรับสถานะ */
    --success-color: #00B894 !important;        /* เขียวสำเร็จ */
    --warning-color: #FDCB6E !important;        /* เหลืองเตือน */
    --danger-color: #E17055 !important;         /* แดงอ่อน */
    --info-color: #74B9FF !important;           /* ฟ้าข้อมูล */

    /* เงา - โทนอ่อนโยน */
    --admin-shadow-soft: 0 4px 20px rgba(52, 152, 219, 0.1) !important;
    --admin-shadow-medium: 0 8px 30px rgba(52, 152, 219, 0.15) !important;
    --admin-shadow-strong: 0 12px 40px rgba(52, 152, 219, 0.2) !important;

    /* พื้นหลัง */
    --bg-primary: #F8FBFF !important;           /* พื้นหลังหลัก */
    --bg-secondary: #E8F4FD !important;         /* พื้นหลังรอง */
    --text-primary: #2D3436 !important;         /* ข้อความหลัก */
    --text-secondary: #636E72 !important;       /* ข้อความรอง */
}

/* ========== Sidebar - Dark Theme ========== */
.sidebar {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 260px !important;
    z-index: 1000 !important;
    height: 100vh !important;
    overflow-y: auto !important;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.15) !important;
    border-right: 2px solid #e2e8f0 !important;
}

/* ========== Sidebar Navigation - Dark Theme with White Text ========== */
.sidebar .nav-link {
    color: #ffffff !important;
    border-radius: 12px !important;
    margin: 3px 15px !important;
    padding: 12px 16px !important;
    transition: all 0.3s ease !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    border: none !important;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
    text-decoration: none !important;
    box-shadow: 0 2px 8px rgba(30, 41, 59, 0.3) !important;
    margin-bottom: 8px !important;
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
    color: #ffffff !important;
    transform: translateX(5px) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(15, 23, 42, 0.4) !important;
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    border-radius: 12px !important;
    border-left: 4px solid #60a5fa !important;
    box-shadow: 0 4px 15px rgba(29, 78, 216, 0.4) !important;
}

.sidebar .nav-link i {
    margin-right: 12px !important;
    font-size: 1.1rem !important;
    width: 20px !important;
    text-align: center !important;
    color: #ffffff !important;
    opacity: 0.9 !important;
}

.sidebar-brand {
    color: #ffffff !important;
    text-align: center !important;
    padding: 30px 20px 25px !important;
    border-bottom: 2px solid #e2e8f0 !important;
    margin-bottom: 15px !important;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3) !important;
}

.sidebar-brand h5 {
    font-weight: 700 !important;
    margin-bottom: 5px !important;
    font-size: 1.2rem !important;
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.sidebar-brand small {
    opacity: 0.9 !important;
    font-size: 0.8rem !important;
    font-weight: 400 !important;
    color: #e0f2fe !important;
}

.sidebar-brand i {
    font-size: 1.8rem !important;
    margin-bottom: 8px !important;
    display: block !important;
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* ========== Sidebar Additional Styles ========== */
.sidebar ul {
    padding: 10px 0 !important;
    margin: 0 !important;
}

.sidebar .nav-item {
    margin: 2px 0 !important;
}

.sidebar .nav-link:first-child {
    margin-top: 5px !important;
}

.sidebar .nav-link:last-child {
    margin-bottom: 15px !important;
}

.sidebar hr {
    border-color: #e2e8f0 !important;
    margin: 15px 20px !important;
    opacity: 0.6 !important;
}

/* เพิ่มเอฟเฟกต์เมื่อ hover */
.sidebar .nav-link:hover i {
    transform: scale(1.1) !important;
    transition: transform 0.3s ease !important;
}

/* ปรับฟอนต์ให้ชัดเจน */
.sidebar .nav-link span {
    font-weight: 600 !important;
    color: #ffffff !important;
}

/* ปรับปรุงการแสดงผลของ nav items */
.sidebar .nav {
    position: relative !important;
    z-index: 1 !important;
}

/* เพิ่มเอฟเฟกต์ขอบมน */
.sidebar .nav-link {
    border: 1px solid rgba(30, 58, 138, 0.5) !important;
}

.sidebar .nav-link:hover {
    border: 1px solid rgba(30, 64, 175, 0.7) !important;
}

.sidebar .nav-link.active {
    border: 1px solid rgba(29, 78, 216, 0.8) !important;
}

/* สำหรับ responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
        border-radius: 0 18px 18px 0 !important;
    }

    .sidebar.show {
        transform: translateX(0) !important;
    }

    .sidebar-brand {
        border-radius: 0 18px 0 0 !important;
        padding: 20px 15px !important;
    }

    .sidebar .nav-link {
        margin: 3px 10px !important;
        padding: 12px 15px !important;
        font-size: 0.9rem !important;
        border-radius: 12px !important;
    }
}

/* ========== Main Content Wrapper - Blue Theme ========== */
.main-content-wrapper {
    margin-left: 260px !important;
    width: calc(100% - 260px) !important;
}

/* ========== Responsive Adjustments ========== */
@media (max-width: 768px) {
    .main-content-wrapper {
        margin-left: 0 !important;
        width: 100% !important;
    }
}

/* ========== Stats Cards - Child-Friendly Theme ========== */
.stats-card.news-card {
    background: linear-gradient(135deg, #3498DB, #74B9FF) !important;
    color: white !important;
}

.stats-card.staff-card {
    background: linear-gradient(135deg, #A8E6CF, #BAFFC9) !important;
    color: #2D3436 !important;
}

.stats-card.images-card {
    background: linear-gradient(135deg, #FFB3BA, #FFDFBA) !important;
    color: #2D3436 !important;
}

.stats-card.categories-card {
    background: linear-gradient(135deg, #D4BAFF, #E8F4FD) !important;
    color: #2D3436 !important;
}

/* ========== Buttons - Simple & Clean Theme ========== */
.btn-primary {
    background: #6c757d !important;
    border: 1px solid #6c757d !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
    box-shadow: none !important;
}

.btn-primary:hover {
    background: #5a6268 !important;
    border-color: #5a6268 !important;
    color: white !important;
    text-decoration: none !important;
    transform: none !important;
    box-shadow: none !important;
}

.btn-secondary {
    background: #495057 !important;
    border: 1px solid #495057 !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-secondary:hover {
    background: #343a40 !important;
    border-color: #343a40 !important;
    color: white !important;
    text-decoration: none !important;
    transform: none !important;
}

.btn-info {
    background: #17a2b8 !important;
    border: 1px solid #17a2b8 !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-info:hover {
    background: #138496 !important;
    border-color: #138496 !important;
    color: white !important;
    text-decoration: none !important;
}

.btn-success {
    background: #28a745 !important;
    border: 1px solid #28a745 !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-success:hover {
    background: #218838 !important;
    border-color: #218838 !important;
    color: white !important;
    text-decoration: none !important;
}

.btn-warning {
    background: #ffc107 !important;
    border: 1px solid #ffc107 !important;
    color: #212529 !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-warning:hover {
    background: #e0a800 !important;
    border-color: #e0a800 !important;
    color: #212529 !important;
    text-decoration: none !important;
}

.btn-danger {
    background: #dc3545 !important;
    border: 1px solid #dc3545 !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-danger:hover {
    background: #c82333 !important;
    border-color: #c82333 !important;
    color: white !important;
    text-decoration: none !important;
}

/* ========== Outline Buttons ========== */
.btn-outline-primary {
    background: transparent !important;
    border: 1px solid #6c757d !important;
    color: #6c757d !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-outline-primary:hover {
    background: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
    text-decoration: none !important;
}

.btn-outline-secondary {
    background: transparent !important;
    border: 1px solid #495057 !important;
    color: #495057 !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-outline-secondary:hover {
    background: #495057 !important;
    border-color: #495057 !important;
    color: white !important;
    text-decoration: none !important;
}

.btn-outline-success {
    background: transparent !important;
    border: 1px solid #28a745 !important;
    color: #28a745 !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-outline-success:hover {
    background: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
    text-decoration: none !important;
}

.btn-outline-danger {
    background: transparent !important;
    border: 1px solid #dc3545 !important;
    color: #dc3545 !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-outline-danger:hover {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
    text-decoration: none !important;
}

.btn-outline-info {
    background: transparent !important;
    border: 1px solid #17a2b8 !important;
    color: #17a2b8 !important;
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

.btn-outline-info:hover {
    background: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: white !important;
    text-decoration: none !important;
}

/* ========== Button Sizes ========== */
.btn-sm {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.8125rem !important;
    border-radius: 4px !important;
}

.btn-lg {
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
    border-radius: 4px !important;
}

/* ========== General Button Styles ========== */
.btn {
    border-radius: 4px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
    display: inline-block !important;
    text-align: center !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    border: 1px solid transparent !important;
    line-height: 1.5 !important;
}

.btn:focus,
.btn:active {
    box-shadow: none !important;
    outline: none !important;
}

/* ========== Cards - Child-Friendly Theme ========== */
.card {
    box-shadow: 0 8px 40px rgba(52, 152, 219, 0.08) !important;
    border-radius: 15px !important;
    border: none !important;
    background: #FFFFFF !important;
}

.card:hover {
    box-shadow: 0 15px 50px rgba(52, 152, 219, 0.15) !important;
    transform: translateY(-3px) !important;
    transition: all 0.3s ease !important;
}

.card-header {
    background: linear-gradient(135deg, #3498DB, #74B9FF) !important;
    color: white !important;
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none !important;
}

.card-body {
    padding: 1.5rem !important;
    background: #FFFFFF !important;
}

/* ========== Dashboard Welcome - Child-Friendly Theme ========== */
.dashboard-welcome {
    background: linear-gradient(135deg,
        #3498DB 0%,
        #74B9FF 50%,
        #A8E6CF 100%) !important;
    box-shadow: 0 15px 50px rgba(52, 152, 219, 0.2) !important;
    border-radius: 20px !important;
    color: white !important;
}

/* ========== Body Background - White Theme ========== */
body.childcare-theme {
    background: #ffffff !important;
    min-height: 100vh !important;
}

.main-content-wrapper {
    background: #ffffff !important;
}

.container-fluid {
    background: #ffffff !important;
}

.main-content {
    background: #ffffff !important;
    min-height: 100vh !important;
}

/* ========== Progress Bars - Child-Friendly Theme ========== */
.progress-bar {
    background: linear-gradient(90deg, #3498DB, #74B9FF) !important;
    border-radius: 10px !important;
}

.progress {
    border-radius: 10px !important;
    background-color: #E8F4FD !important;
}

/* ========== Badges - Child-Friendly Theme ========== */
.badge.bg-primary {
    background: linear-gradient(135deg, #3498DB, #74B9FF) !important;
    border-radius: 8px !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #00B894, #A8E6CF) !important;
    border-radius: 8px !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #FDCB6E, #FFDFBA) !important;
    color: #2D3436 !important;
    border-radius: 8px !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #74B9FF, #A8E6CF) !important;
    color: white !important;
    border-radius: 8px !important;
}

/* ========== Links - Child-Friendly Theme ========== */
a {
    color: #3498DB !important;
    transition: all 0.3s ease !important;
}

a:hover {
    color: #2980B9 !important;
    text-decoration: none !important;
}

/* ========== Form Controls - Child-Friendly Theme ========== */
.form-control {
    border-radius: 10px !important;
    border: 2px solid #E8F4FD !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    border-color: #3498DB !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.15) !important;
    background-color: #F8FBFF !important;
}

.form-check-input:checked {
    background-color: #3498DB !important;
    border-color: #3498DB !important;
}

.form-select {
    border-radius: 10px !important;
    border: 2px solid #E8F4FD !important;
}

/* ========== Tables - Child-Friendly Theme ========== */
.table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(52, 152, 219, 0.08) !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(52, 152, 219, 0.03) !important;
}

.table th {
    background: linear-gradient(135deg, #E8F4FD, #F8FBFF) !important;
    color: #2D3436 !important;
    border-bottom: 2px solid #74B9FF !important;
    font-weight: 600 !important;
}

.table td {
    border-color: #E8F4FD !important;
    vertical-align: middle !important;
}

/* ========== Alerts - Child-Friendly Theme ========== */
.alert {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
}

.alert-primary {
    background: linear-gradient(135deg, #E8F4FD, #F8FBFF) !important;
    color: #2D3436 !important;
    border-left: 4px solid #3498DB !important;
}

.alert-success {
    background: linear-gradient(135deg, #D1F2EB, #A8E6CF) !important;
    color: #2D3436 !important;
    border-left: 4px solid #00B894 !important;
}

.alert-warning {
    background: linear-gradient(135deg, #FFF3CD, #FFDFBA) !important;
    color: #2D3436 !important;
    border-left: 4px solid #FDCB6E !important;
}

.alert-danger {
    background: linear-gradient(135deg, #F8D7DA, #FFB3BA) !important;
    color: #2D3436 !important;
    border-left: 4px solid #E17055 !important;
}

.alert-info {
    background: linear-gradient(135deg, #D1ECF1, #E8F4FD) !important;
    color: #2D3436 !important;
    border-left: 4px solid #74B9FF !important;
}

/* ========== Modal - Child-Friendly Theme ========== */
.modal-content {
    border-radius: 20px !important;
    border: none !important;
    box-shadow: 0 20px 60px rgba(52, 152, 219, 0.15) !important;
}

.modal-header {
    background: linear-gradient(135deg, #3498DB, #74B9FF) !important;
    color: white !important;
    border-radius: 20px 20px 0 0 !important;
    border-bottom: none !important;
}

.modal-body {
    padding: 2rem !important;
    background: #F8FBFF !important;
}

.modal-footer {
    background: #F8FBFF !important;
    border-top: none !important;
    border-radius: 0 0 20px 20px !important;
    padding: 1.5rem 2rem !important;
}

/* ========== Dropdown - Child-Friendly Theme ========== */
.dropdown-menu {
    border-radius: 15px !important;
    border: none !important;
    box-shadow: 0 10px 30px rgba(52, 152, 219, 0.15) !important;
    background: white !important;
}

.dropdown-item {
    border-radius: 8px !important;
    margin: 2px 8px !important;
    padding: 10px 15px !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #74B9FF, #A8E6CF) !important;
    color: white !important;
    transform: translateX(5px) !important;
}

/* ========== Pagination - Child-Friendly Theme ========== */
.pagination .page-link {
    border-radius: 10px !important;
    margin: 0 3px !important;
    border: 2px solid #E8F4FD !important;
    color: #3498DB !important;
    transition: all 0.3s ease !important;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #74B9FF, #3498DB) !important;
    color: white !important;
    border-color: #3498DB !important;
    transform: translateY(-2px) !important;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #3498DB, #74B9FF) !important;
    border-color: #3498DB !important;
}
    border-color: #AED6F1 !important;
    color: #2C3E50 !important;
}

/* ========== Navigation - Blue Theme ========== */
.nav-pills .nav-link.active {
    background: linear-gradient(135deg, #3498DB, #5DADE2) !important;
}

.nav-pills .nav-link:hover {
    background: rgba(52, 152, 219, 0.1) !important;
}

/* ========== Dropdown - Blue Theme ========== */
.dropdown-menu {
    box-shadow: 0 8px 30px rgba(52, 152, 219, 0.15) !important;
}

.dropdown-item:hover {
    background: rgba(52, 152, 219, 0.1) !important;
}

/* ========== Modal - Blue Theme ========== */
.modal-header {
    background: linear-gradient(135deg, #3498DB, #5DADE2) !important;
    color: white !important;
}

/* ========== Pagination - Blue Theme ========== */
.page-link {
    color: #3498DB !important;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #3498DB, #5DADE2) !important;
    border-color: #3498DB !important;
}

/* ========== Spinner - Blue Theme ========== */
.spinner-border {
    color: #3498DB !important;
}

/* ========== Text Colors - Blue Theme ========== */
.text-primary {
    color: #3498DB !important;
}

.text-info {
    color: #5DADE2 !important;
}

/* ========== Background Colors - Blue Theme ========== */
.bg-primary {
    background: linear-gradient(135deg, #3498DB, #5DADE2) !important;
}

.bg-info {
    background: linear-gradient(135deg, #85C1E9, #AED6F1) !important;
}

/* ========== Border Colors - Blue Theme ========== */
.border-primary {
    border-color: #3498DB !important;
}

.border-info {
    border-color: #5DADE2 !important;
}

/* ========== Custom Components - Blue Theme ========== */
.child-card {
    background: rgba(214, 234, 248, 0.3) !important;
    border: 2px solid rgba(52, 152, 219, 0.2) !important;
}

.child-card:hover {
    border-color: #3498DB !important;
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.2) !important;
}

/* ========== Animation Enhancements ========== */
@keyframes blueGlow {
    0% { box-shadow: 0 0 5px rgba(52, 152, 219, 0.3); }
    50% { box-shadow: 0 0 20px rgba(52, 152, 219, 0.6); }
    100% { box-shadow: 0 0 5px rgba(52, 152, 219, 0.3); }
}

.glow-blue {
    animation: blueGlow 2s ease-in-out infinite;
}
