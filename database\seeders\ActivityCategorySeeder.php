<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ActivityCategory;

class ActivityCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = [
            [
                'name' => 'กิจกรรมการเรียนรู้',
                'description' => 'รูปภาพกิจกรรมการเรียนรู้และพัฒนาทักษะต่างๆ ของเด็ก',
                'slug' => 'learning-activities',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'name' => 'กิจกรรมกีฬา',
                'description' => 'รูปภาพกิจกรรมกีฬาและการออกกำลังกายของเด็ก',
                'slug' => 'sports-activities',
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'name' => 'กิจกรรมศิลปะ',
                'description' => 'รูปภาพกิจกรรมศิลปะ การวาดภาพ และงานฝีมือของเด็ก',
                'slug' => 'art-activities',
                'sort_order' => 3,
                'is_active' => true
            ],
            [
                'name' => 'กิจกรรมพิเศษ',
                'description' => 'รูปภาพกิจกรรมพิเศษต่างๆ เช่น วันสำคัญ งานเฉลิมฉลอง',
                'slug' => 'special-activities',
                'sort_order' => 4,
                'is_active' => true
            ],
            [
                'name' => 'กิจกรรมกลางแจ้ง',
                'description' => 'รูปภาพกิจกรรมกลางแจ้งและการเล่นในสวน',
                'slug' => 'outdoor-activities',
                'sort_order' => 5,
                'is_active' => true
            ]
        ];

        foreach ($categories as $category) {
            ActivityCategory::create($category);
        }
    }
}
