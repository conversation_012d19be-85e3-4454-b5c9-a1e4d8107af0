@extends('layouts.admin')

@section('title', 'รายละเอียดข่าว - ระบบจัดการ')
@section('page-title', 'รายละเอียดข่าวประชาสัมพันธ์')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-newspaper me-2"></i>{{ $news->title }}
                </h5>
                <div>
                    <span class="badge {{ $news->is_published ? 'bg-success' : 'bg-warning' }}">
                        {{ $news->is_published ? 'เผยแพร่แล้ว' : 'ร่าง' }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <!-- รูปภาพ -->
                @php
                    $mainImageUrl = null;

                    // ลำดับความสำคัญ: 1. Featured image 2. รูปแรกใน collection
                    if ($news->featuredImage) {
                        $mainImageUrl = $news->featuredImage->image_url;
                    } elseif ($news->images && $news->images->count() > 0) {
                        $mainImageUrl = $news->images->first()->image_url;
                    }
                @endphp

                @if($mainImageUrl)
                <div class="text-center mb-4">
                    <img src="{{ $mainImageUrl }}"
                         alt="{{ $news->title }}"
                         class="img-fluid rounded shadow"
                         style="max-height: 400px;">
                </div>
                @endif
                
                <!-- เนื้อหา -->
                <div class="mb-4">
                    <h6 class="text-muted mb-2">เนื้อหาข่าว</h6>
                    <div class="content-text">
                        {!! nl2br(e($news->content)) !!}
                    </div>
                </div>
                
                <!-- ข้อมูลเพิ่มเติม -->
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">วันที่สร้าง</h6>
                        <p class="mb-3">
                            <i class="fas fa-calendar me-2"></i>
                            {{ $news->created_at->format('d/m/Y H:i') }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">วันที่อัปเดต</h6>
                        <p class="mb-3">
                            <i class="fas fa-clock me-2"></i>
                            {{ $news->updated_at->format('d/m/Y H:i') }}
                        </p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">วันที่สร้าง</h6>
                        <p class="mb-3">
                            <i class="fas fa-clock me-2"></i>
                            {{ $news->created_at->format('d/m/Y H:i') }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">แก้ไขล่าสุด</h6>
                        <p class="mb-3">
                            <i class="fas fa-edit me-2"></i>
                            {{ $news->updated_at->format('d/m/Y H:i') }}
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="card-footer bg-transparent">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับรายการ
                    </a>
                    <div>
                        <a href="{{ route('admin.news.edit', $news->id) }}" class="btn btn-primary me-2">
                            <i class="fas fa-edit me-2"></i>แก้ไข
                        </a>
                        <button type="button" 
                                class="btn btn-danger"
                                onclick="confirmDelete({{ $news->id }}, '{{ $news->title }}')">
                            <i class="fas fa-trash me-2"></i>ลบ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>ข้อมูลเพิ่มเติม
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">สถานะ</small>
                    <div>
                        @if($news->is_published)
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>เผยแพร่แล้ว
                            </span>
                        @else
                            <span class="badge bg-warning">
                                <i class="fas fa-clock me-1"></i>ร่าง
                            </span>
                        @endif
                    </div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">ลิงก์ดูข่าว</small>
                    <div>
                        <a href="{{ route('news.show', $news->id) }}" 
                           target="_blank" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt me-1"></i>ดูในเว็บไซต์
                        </a>
                    </div>
                </div>
                

            </div>
        </div>
        
        <!-- การดำเนินการอื่นๆ -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>การดำเนินการ
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.news.images.manage', $news->id) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-images me-1"></i>จัดการรูปภาพ
                    </a>

                    @if($news->is_published)
                        <form action="{{ route('admin.news.update', $news->id) }}" method="POST" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="title" value="{{ $news->title }}">
                            <input type="hidden" name="content" value="{{ $news->content }}">

                            <input type="hidden" name="is_published" value="0">
                            <button type="submit" class="btn btn-warning btn-sm w-100">
                                <i class="fas fa-eye-slash me-1"></i>ยกเลิกการเผยแพร่
                            </button>
                        </form>
                    @else
                        <form action="{{ route('admin.news.update', $news->id) }}" method="POST" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="title" value="{{ $news->title }}">
                            <input type="hidden" name="content" value="{{ $news->content }}">

                            <input type="hidden" name="is_published" value="1">
                            <button type="submit" class="btn btn-success btn-sm w-100">
                                <i class="fas fa-eye me-1"></i>เผยแพร่ทันที
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ยืนยันการลบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>คุณต้องการลบข่าว "<span id="deleteItemName"></span>" หรือไม่?</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    การดำเนินการนี้ไม่สามารถยกเลิกได้
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>ลบ
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function confirmDelete(id, title) {
    if (confirm('คุณต้องการลบข่าว "' + title + '" หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')) {
        // สร้าง form แบบ dynamic
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/news/' + id;

        // เพิ่ม CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // เพิ่ม method DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // เพิ่ม form ไปยัง body และ submit
        document.body.appendChild(form);
        form.submit();
    }
}


</script>
@endsection
