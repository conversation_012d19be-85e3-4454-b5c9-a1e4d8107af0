/* ========== Clean Pagination Styles ========== */

.pagination-wrapper {
    margin: 2rem 0;
    text-align: center;
    padding: 0.5rem 0;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.modern-pagination {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
    list-style: none;
}

.modern-pagination .page-item {
    margin: 0;
}

.modern-pagination .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    padding: 0.5rem 0.75rem;
    margin: 0;
    font-size: 0.875rem;
    font-weight: 400;
    color: #6c757d;
    text-decoration: none;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    transition: all 0.15s ease;
}

.modern-pagination .page-link:hover {
    color: #495057;
    background: #f8f9fa;
    border-color: #d0d0d0;
}

.modern-pagination .page-item.active .page-link {
    color: #ffffff;
    background: #007bff;
    border-color: #007bff;
    font-weight: 500;
}

.modern-pagination .page-item.disabled .page-link {
    color: #adb5bd;
    background: #ffffff;
    border-color: #e0e0e0;
    cursor: not-allowed;
    opacity: 0.6;
}

.modern-pagination .page-link.prev-next {
    min-width: auto;
    padding: 0.5rem 0.875rem;
    gap: 0.375rem;
    font-weight: 400;
    color: #6c757d;
    background: #ffffff;
    border-color: #e0e0e0;
}

.modern-pagination .page-link.prev-next:hover {
    color: #495057;
    background: #f8f9fa;
    border-color: #d0d0d0;
}

.modern-pagination .page-item.disabled .page-link.prev-next {
    color: #adb5bd;
    background: #ffffff;
    border-color: #e0e0e0;
}

.modern-pagination .page-link.dots {
    background: transparent;
    border: none;
    cursor: default;
    font-weight: 400;
    color: #6c757d;
}

.pagination-info {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    display: inline-block;
}

.pagination-info p {
    margin: 0;
    font-weight: 400;
    color: #6c757d;
    font-size: 0.875rem;
}

/* ========== Responsive Design ========== */
@media (max-width: 768px) {
    .modern-pagination {
        gap: 0.375rem;
    }

    .modern-pagination .page-link {
        min-width: 32px;
        height: 32px;
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }

    .modern-pagination .page-link.prev-next {
        padding: 0.375rem 0.625rem;
        gap: 0.25rem;
    }

    .pagination-info {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .modern-pagination {
        gap: 0.25rem;
    }

    .modern-pagination .page-link {
        min-width: 32px;
        height: 32px;
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .modern-pagination .page-link.prev-next {
        padding: 0.25rem 0.625rem;
    }

    .pagination-wrapper {
        margin: 1.5rem 0;
    }

    .pagination-info {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
}


