@extends('layouts.admin')

@section('title', 'ดูหมวดหมู่รูปภาพ - ระบบจัดการ')
@section('page-title', 'รูปภาพในหมวดหมู่: ' . $category->name)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-1">{{ $category->name }}</h4>
        @if($category->description)
            <p class="text-muted mb-0">{{ $category->description }}</p>
        @endif
    </div>
    <div>
        <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-2"></i>แก้ไข
        </a>
        <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>กลับ
        </a>
    </div>
</div>

<!-- Category Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="h3 mb-1 text-primary">{{ $images->total() }}</div>
                <div class="small text-muted">รูปภาพทั้งหมด</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="h3 mb-1 text-success">{{ $category->images()->published()->count() }}</div>
                <div class="small text-muted">เผยแพร่แล้ว</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="h3 mb-1 text-warning">{{ $category->images()->where('is_published', false)->count() }}</div>
                <div class="small text-muted">ร่าง</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="h3 mb-1 text-info">{{ $category->sort_order }}</div>
                <div class="small text-muted">ลำดับ</div>
            </div>
        </div>
    </div>
</div>

<!-- Images Grid -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">รูปภาพในหมวดหมู่</h6>
        <a href="{{ route('admin.images.create') }}?category_id={{ $category->id }}" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i>เพิ่มรูปภาพ
        </a>
    </div>
    <div class="card-body">
        @if($images->count() > 0)
            <div class="row g-4">
                @foreach($images as $image)
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="card h-100">
                        <div class="position-relative">
                            <img src="{{ $image->thumbnail_url ?? $image->image_url }}"
                                 alt="{{ $image->description ?? 'รูปภาพกิจกรรม' }}"
                                 class="card-img-top"
                                 style="height: 200px; object-fit: cover; object-position: center center;">
                            
                            @if(!$image->is_published)
                                <span class="position-absolute top-0 start-0 badge bg-warning m-2">
                                    ร่าง
                                </span>
                            @endif
                        </div>
                        
                        <div class="card-body">
                            @if($image->description)
                                <p class="card-text small">{{ Str::limit($image->description, 50) }}</p>
                            @else
                                <p class="card-text small text-muted">ไม่มีคำอธิบาย</p>
                            @endif
                            
                            <div class="small text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $image->created_at->format('d/m/Y') }}
                            </div>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ route('admin.images.show', $image) }}" 
                                   class="btn btn-sm btn-outline-info" title="ดู">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.images.edit', $image) }}" 
                                   class="btn btn-sm btn-outline-primary" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        title="ลบ"
                                        onclick="confirmDelete({{ $image->id }}, '{{ $image->description ?? 'รูปภาพนี้' }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $images->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-images fa-5x text-muted mb-4"></i>
                <h5 class="text-muted">ยังไม่มีรูปภาพในหมวดหมู่นี้</h5>
                <p class="text-muted">เริ่มต้นโดยการเพิ่มรูปภาพแรกของคุณ</p>
                <a href="{{ route('admin.images.create') }}?category_id={{ $category->id }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพ
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ยืนยันการลบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>คุณแน่ใจหรือไม่ที่จะลบ <strong id="imageName"></strong>?</p>
                <p class="text-danger small">การลบรูปภาพจะไม่สามารถกู้คืนได้</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">ลบ</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function confirmDelete(imageId, imageName) {
    document.getElementById('imageName').textContent = imageName;
    document.getElementById('deleteForm').action = `/admin/images/${imageId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endsection
