<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Staff;
use Illuminate\Support\Facades\File;

class SyncStaffImages extends Command
{
    protected $signature = 'staff:sync-images';
    protected $description = 'Sync staff images from storage/app/public to public/storage';

    public function handle()
    {
        $this->info('Starting to sync staff images...');

        $staffMembers = Staff::whereNotNull('photo')->get();
        $synced = 0;
        $errors = 0;

        foreach ($staffMembers as $staff) {
            $this->info("Processing staff ID: {$staff->id} - {$staff->name}");

            if ($this->syncFile($staff->photo)) {
                $this->info("  - ✅ Synced: {$staff->photo}");
                $synced++;
            } else {
                $this->error("  - ❌ Failed to sync: {$staff->photo}");
                $errors++;
            }
        }

        $this->info("\n=== Summary ===");
        $this->info("Total staff processed: " . $staffMembers->count());
        $this->info("Files synced: {$synced}");
        $this->info("Errors: {$errors}");

        if ($synced > 0) {
            $this->info("\n✅ Staff image sync completed successfully!");
        } else {
            $this->warn("\n⚠️  No files were synced.");
        }

        return 0;
    }

    /**
     * ซิงค์ไฟล์จาก storage/app/public ไปยัง public/storage
     */
    private function syncFile($imagePath)
    {
        if (!$imagePath) {
            return false;
        }

        $sourcePath = storage_path('app/public/' . str_replace('\\', '/', $imagePath));
        $targetPath = public_path('storage/' . str_replace('\\', '/', $imagePath));

        // ตรวจสอบว่าไฟล์ต้นฉบับมีอยู่หรือไม่
        if (!file_exists($sourcePath)) {
            $this->warn("    Source file not found: {$sourcePath}");
            return false;
        }

        // ตรวจสอบว่าไฟล์ปลายทางมีอยู่แล้วหรือไม่
        if (file_exists($targetPath)) {
            $this->info("    Target file already exists, skipping: {$targetPath}");
            return true;
        }

        // สร้างโฟลเดอร์ target หากไม่มี
        $targetDir = dirname($targetPath);
        if (!file_exists($targetDir)) {
            if (!mkdir($targetDir, 0755, true)) {
                $this->error("    Failed to create directory: {$targetDir}");
                return false;
            }
        }

        // คัดลอกไฟล์
        if (copy($sourcePath, $targetPath)) {
            $this->info("    Copied: {$sourcePath} -> {$targetPath}");
            return true;
        } else {
            $this->error("    Failed to copy file");
            return false;
        }
    }
}
