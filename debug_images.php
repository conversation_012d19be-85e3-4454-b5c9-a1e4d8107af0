<?php
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\News;
use App\Models\NewsImage;

echo "=== ตรวจสอบรูปภาพข่าว ===\n\n";

// ตรวจสอบข่าวทั้งหมด
$news = News::with('images')->get();

foreach ($news as $newsItem) {
    echo "News ID: {$newsItem->id}\n";
    echo "Title: {$newsItem->title}\n";
    echo "Main Image: " . ($newsItem->image ?? 'null') . "\n";
    
    if ($newsItem->image) {
        $mainImagePath = public_path('storage/' . $newsItem->image);
        echo "Main Image Exists: " . (file_exists($mainImagePath) ? 'YES' : 'NO') . "\n";
        echo "Main Image Full Path: {$mainImagePath}\n";
    }
    
    echo "Images Count: {$newsItem->images->count()}\n";
    
    if ($newsItem->images->count() > 0) {
        foreach ($newsItem->images as $index => $image) {
            echo "  Image {$index}: {$image->image_path}\n";
            $imagePath = public_path('storage/' . $image->image_path);
            echo "  Exists: " . (file_exists($imagePath) ? 'YES' : 'NO') . "\n";
            echo "  Full Path: {$imagePath}\n";
            
            if ($image->thumbnail_path) {
                echo "  Thumbnail: {$image->thumbnail_path}\n";
                $thumbPath = public_path('storage/' . $image->thumbnail_path);
                echo "  Thumbnail Exists: " . (file_exists($thumbPath) ? 'YES' : 'NO') . "\n";
            }
        }
    }
    
    echo "---\n\n";
}

// ตรวจสอบไฟล์ที่มีอยู่ในโฟลเดอร์
echo "=== ไฟล์ที่มีอยู่ในโฟลเดอร์ ===\n";
$newsDir = public_path('storage/news');
if (is_dir($newsDir)) {
    $files = scandir($newsDir);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            echo "File: {$file}\n";
        }
    }
}
