@extends('layouts.app')

@section('title', 'ข่าวประชาสัมพันธ์ - ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า')

@section('content')
<!-- Page Header -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-newspaper me-3"></i>
                    ข่าวประชาสัมพันธ์
                </h1>
                <p class="lead">ติดตามข่าวสารและกิจกรรมของศูนย์พัฒนาเด็กเล็ก</p>
            </div>
        </div>
    </div>
</section>

<!-- News Content -->
<section class="py-5">
    <div class="container">
        @if($news->count() > 0)
            <div class="row g-4">
                @foreach($news as $newsItem)
                <div class="col-lg-6">
                    <div class="card news-card h-100">
                        @php
                            $thumbnailUrl = null;

                            // ลำดับความสำคัญ: 1. Featured image 2. รูปแรกใน collection
                            if ($newsItem->featuredImage) {
                                $thumbnailUrl = $newsItem->featuredImage->thumbnail_url ?: $newsItem->featuredImage->image_url;
                            } elseif ($newsItem->images && $newsItem->images->count() > 0) {
                                $thumbnailUrl = $newsItem->images->first()->thumbnail_url ?: $newsItem->images->first()->image_url;
                            }
                        @endphp

                        @if($thumbnailUrl)
                            <div class="position-relative overflow-hidden">
                                <img src="{{ $thumbnailUrl }}"
                                     class="card-img-top news-thumbnail"
                                     alt="{{ $newsItem->title }}"
                                     style="height: 250px; object-fit: cover; object-position: center center;">
                            </div>
                        @else
                            <img src="https://via.placeholder.com/500x250/e9ecef/6c757d?text=ไม่มีรูปภาพ"
                                 class="card-img-top"
                                 alt="ไม่มีรูปภาพ"
                                 style="height: 250px; object-fit: cover;">
                        @endif
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ $newsItem->title }}</h5>
                            <p class="card-text">{{ Str::limit(strip_tags($newsItem->content), 150) }}</p>

                            @if($newsItem->images && $newsItem->images->count() > 0)
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-images me-1"></i>
                                        {{ $newsItem->images->count() }} รูปภาพ
                                    </small>
                                </div>
                            @endif
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <a href="{{ route('news.show', $newsItem->id) }}" class="btn btn-primary">
                                อ่านต่อ <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-5">
                {{ $news->links('custom.pagination') }}
            </div>
        @else
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-5x text-muted mb-4"></i>
                        <h3 class="text-muted">ยังไม่มีข่าวประชาสัมพันธ์</h3>
                        <p class="text-muted">กรุณาติดตามข่าวสารในภายหลัง</p>
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>กลับหน้าหลัก
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>


@endsection

@section('styles')
<style>
/* ป้องกันการกระพริบของรูปภาพ */
.news-thumbnail {
    transition: none !important;
    will-change: auto;
}

.news-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* News card hover effects removed */

/* ป้องกันการกระพริบของรูปภาพทั่วไป */
img {
    image-rendering: auto;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

img.loaded {
    opacity: 1 !important;
    transition: none !important;
}

img.image-loading {
    opacity: 0.8;
    transition: opacity 0.3s ease;
}


</style>
@endsection

@section('scripts')
<script>
// เริ่มต้นเมื่อหน้าเว็บโหลดเสร็จ
document.addEventListener('DOMContentLoaded', function() {
    console.log('News index page loaded');

    // ป้องกันการกระพริบของรูปภาพทั้งหมด
    const images = document.querySelectorAll('img');
    console.log('Found images:', images.length);

    images.forEach((img, index) => {
        console.log(`Image ${index + 1}:`, img.src);

        img.addEventListener('load', function() {
            console.log('Image loaded successfully:', this.src);
            this.style.opacity = '1';
            this.style.transition = 'none';
        });

        img.addEventListener('error', function() {
            console.error('Image failed to load:', this.src);
            this.style.opacity = '0.5';
            // แสดงรูป placeholder แทน
            this.src = 'https://via.placeholder.com/500x250/e9ecef/6c757d?text=ไม่สามารถโหลดรูปภาพได้';
        });

        // ตรวจสอบว่ารูปโหลดแล้วหรือยัง
        if (img.complete) {
            console.log('Image already loaded:', img.src);
            img.style.opacity = '1';
            img.style.transition = 'none';
        }
    });

    // ตรวจสอบ news cards
    const newsCards = document.querySelectorAll('.news-card');
    console.log('Found news cards:', newsCards.length);
});
</script>
@endsection
