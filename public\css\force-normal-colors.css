/* Force Normal Colors CSS */
/* ไฟล์นี้ให้แน่ใจว่ารูปภาพทั้งหมดจะแสดงเป็นสีปกติ */

/* ========== Global Image Color Reset ========== */
/* ลบ global selector เพื่อไม่ให้กระทบกับ interactive elements */
/* * {
    -webkit-filter: none !important;
    filter: none !important;
} */

/* ========== Image Elements ========== */
img,
picture,
svg,
canvas,
video {
    /* ลบ filter ทั้งหมด */
    filter: none !important;
    -webkit-filter: none !important;
    -moz-filter: none !important;
    -ms-filter: none !important;
    -o-filter: none !important;

    /* ให้แน่ใจว่า opacity เป็น 1 */
    opacity: 1 !important;

    /* ลบ effects ที่อาจทำให้สีเปลี่ยน */
    mix-blend-mode: normal !important;
    isolation: auto !important;

    /* ให้แน่ใจว่าการแสดงผลสีถูกต้อง */
    color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;

    /* ลบ transform ที่ใช้ filter เท่านั้น ไม่ลบ transform ทั้งหมด */
    /* transform: none !important; - ลบออกเพื่อไม่ให้กระทบกับ interactive elements */
}

/* ========== Specific Image Classes ========== */
.card-img-top,
.gallery-item img,
.news-image,
.staff-image,
.staff-avatar,
.admin-thumbnail,
.admin-preview,
.portrait-image,
.landscape-image,
.rounded-circle,
.img-fluid,
.img-thumbnail {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
    mix-blend-mode: normal !important;
    isolation: auto !important;
}

/* ========== Hover States ========== */
img:hover,
.card-img-top:hover,
.gallery-item:hover img,
.news-image:hover,
.staff-image:hover,
.staff-avatar:hover,
.admin-thumbnail:hover,
.admin-preview:hover {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
    mix-blend-mode: normal !important;
}

/* ========== Focus States ========== */
img:focus,
.card-img-top:focus,
.gallery-item img:focus,
.staff-avatar:focus,
.admin-thumbnail:focus {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
    mix-blend-mode: normal !important;
}

/* ========== Active States ========== */
img:active,
.card-img-top:active,
.gallery-item img:active,
.admin-thumbnail:active {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
    mix-blend-mode: normal !important;
}

/* ========== Container Elements ========== */
.card,
.gallery-item,
.news-card,
.staff-card,
.image-container,
.modal-body {
    /* ป้องกันการใช้ filter ใน container ที่อาจส่งผลต่อรูปภาพ */
    filter: none !important;
    -webkit-filter: none !important;
}

/* ========== Override Bootstrap Classes ========== */
.img-grayscale,
.grayscale,
.sepia,
.blur,
.brightness,
.contrast {
    filter: none !important;
    -webkit-filter: none !important;
}

/* ========== Print Styles ========== */
@media print {
    img,
    .card-img-top,
    .gallery-item img,
    .staff-avatar {
        filter: none !important;
        -webkit-filter: none !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
}

/* ========== High DPI Displays ========== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    img,
    .card-img-top,
    .gallery-item img,
    .staff-avatar {
        filter: none !important;
        -webkit-filter: none !important;
    }
}

/* ========== Dark Mode Override ========== */
@media (prefers-color-scheme: dark) {
    img,
    .card-img-top,
    .gallery-item img,
    .staff-avatar {
        filter: none !important;
        -webkit-filter: none !important;
        opacity: 1 !important;
    }
}

/* ========== Animation Override ========== */
@keyframes fadeIn {
    from { opacity: 0; filter: none !important; }
    to { opacity: 1; filter: none !important; }
}

@keyframes slideIn {
    from { transform: translateY(20px); filter: none !important; }
    to { transform: translateY(0); filter: none !important; }
}

/* ========== Utility Classes ========== */
.force-normal-color {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
    mix-blend-mode: normal !important;
}

.no-filter {
    filter: none !important;
    -webkit-filter: none !important;
}

.full-opacity {
    opacity: 1 !important;
}

/* ========== Error States Override ========== */
.image-error,
.img-error,
.broken-image {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
}

/* ========== Loading States Override ========== */
.image-loading,
.img-loading,
.lazy {
    filter: none !important;
    -webkit-filter: none !important;
}

.image-loading.loaded,
.img-loading.loaded,
.lazy.loaded {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
}

/* ========== Modal Override ========== */
.modal img,
.modal .card-img-top,
.modal-body img {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
    mix-blend-mode: normal !important;
}

/* ========== Carousel Override ========== */
.carousel img,
.carousel-item img {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
}

/* ========== Gallery Override ========== */
.gallery img,
.gallery-grid img,
.photo-gallery img {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
}

/* ========== Admin Panel Override ========== */
.admin-panel img,
.dashboard img,
.admin-content img {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
}

/* ========== Important Override for All ========== */
* img,
* picture,
* svg {
    filter: none !important;
    -webkit-filter: none !important;
}
