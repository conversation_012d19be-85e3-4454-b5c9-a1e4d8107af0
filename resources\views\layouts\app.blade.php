<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า')</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Image Optimization CSS -->
    <link href="{{ asset('css/image-optimization.css') }}" rel="stylesheet">
    <!-- Force Normal Colors CSS -->
    <link href="{{ asset('css/force-normal-colors.css') }}" rel="stylesheet">
    <!-- Modern Pagination CSS -->
    <link href="{{ asset('css/modern-pagination.css') }}" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Prompt', sans-serif;
            line-height: 1.6;
            padding-top: 85px;
        }

        .navbar-brand {
            font-weight: 600;
            color: #1e293b !important;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .navbar-brand:hover {
            color: #3b82f6 !important;
            text-decoration: none;
        }

        .navbar-brand i {
            color: #3b82f6;
            margin-right: 12px;
            font-size: 2rem;
        }

        .brand-text {
            display: flex;
            flex-direction: column;
            line-height: 1.1;
        }

        .brand-main {
            font-size: 1.4rem;
            font-weight: 700;
            color: #1e293b;
        }

        .brand-subtitle {
            font-size: 0.8rem;
            color: #64748b;
            font-weight: 400;
            margin-top: 2px;
        }

        .navbar {
            padding: 1rem 0;
            background: #ffffff !important;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .navbar-nav {
            gap: 0.5rem;
        }

        .nav-link {
            font-weight: 500;
            color: #64748b !important;
            padding: 0.75rem 1rem !important;
            border-radius: 6px;
            white-space: nowrap;
            font-size: 0.95rem;
        }

        .nav-link:hover {
            background: #f8fafc;
            color: #1e293b !important;
        }

        .nav-link.active {
            background: #3b82f6;
            color: #ffffff !important;
        }

        .nav-link i {
            margin-right: 6px;
        }

        .nav-link.active i {
            color: #ffffff;
        }

        /* Login Button - Simple & Clean */
        .login-btn {
            background: #6c757d !important;
            color: white !important;
            border: 1px solid #6c757d !important;
            border-radius: 4px !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            font-size: 0.875rem !important;
            text-decoration: none !important;
            transition: all 0.2s ease !important;
        }

        .login-btn:hover {
            background: #5a6268 !important;
            border-color: #5a6268 !important;
            color: white !important;
            text-decoration: none !important;
        }

        .login-btn i {
            margin-right: 6px !important;
        }

        .admin-dropdown {
            background: #495057 !important;
            color: white !important;
            border: 1px solid #495057 !important;
            border-radius: 4px !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            font-size: 0.875rem !important;
            text-decoration: none !important;
            transition: all 0.2s ease !important;
        }

        .admin-dropdown:hover {
            background: #343a40 !important;
            border-color: #343a40 !important;
            color: white !important;
            text-decoration: none !important;
        }

        /* Dropdown Menu */
        .dropdown-menu {
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            padding: 0.5rem 0;
            margin-top: 0.5rem;
            background: #ffffff;
        }

        .dropdown-item {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            color: #1e293b;
        }

        .dropdown-item:hover {
            background: #f8fafc;
            color: #1e293b;
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border-color: #e2e8f0;
        }

        /* Mobile Toggle */
        .navbar-toggler {
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 6px 10px;
        }

        .navbar-toggler:hover {
            border-color: #3b82f6;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2830, 41, 59, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* Responsive Design */
        @media (max-width: 991.98px) {
            .navbar-collapse {
                background: #ffffff;
                border-radius: 6px;
                margin-top: 1rem;
                padding: 1rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border: 1px solid #e2e8f0;
            }

            .navbar-nav {
                gap: 0.25rem;
            }

            .nav-link {
                padding: 0.75rem 1rem !important;
                margin: 0.125rem 0;
                border-radius: 6px;
            }

            .brand-subtitle {
                display: none !important;
            }

            .brand-main {
                font-size: 1.2rem;
            }
        }

        @media (max-width: 767.98px) {
            body {
                padding-top: 75px;
            }

            .navbar {
                padding: 0.75rem 0;
            }

            .brand-main {
                font-size: 1.1rem;
            }

            .navbar-brand i {
                font-size: 1.75rem;
                margin-right: 8px;
            }
        }

        /* Fade in animation for content (optional) */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
            padding: 0.5rem;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* Responsive Design */
        @media (max-width: 991.98px) {
            .navbar-nav {
                text-align: center;
                margin-top: 1rem;
            }

            .nav-link {
                margin: 0.25rem 0;
                display: block;
            }

            .brand-main {
                font-size: 1.2rem;
            }

            .brand-subtitle {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 575.98px) {
            .navbar {
                padding: 0.75rem 0;
            }

            .brand-main {
                font-size: 1.1rem;
            }

            .brand-subtitle {
                display: none;
            }

            .nav-link {
                padding: 0.6rem 1rem !important;
                font-size: 0.95rem;
            }
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 60px 0 20px 0;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .footer-section {
            margin-bottom: 25px;
        }

        .footer-title {
            color: #ecf0f1;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 8px;
        }

        .footer-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .footer-contact-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 6px;
            padding: 2px 0;
            transition: all 0.3s ease;
            line-height: 1.5;
            font-size: 0.95rem;
        }

        .footer-contact-item:hover {
            transform: translateX(5px);
            color: #ecf0f1;
        }

        .footer-contact-item i {
            width: 20px;
            margin-right: 12px;
            color: #667eea;
            margin-top: 2px;
        }

        .footer-menu-item {
            margin-bottom: 8px;
        }

        .footer-menu-item a {
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            padding: 5px 0;
        }

        .footer-menu-item a:hover {
            color: #ecf0f1;
            transform: translateX(5px);
        }

        .footer-menu-item a i {
            margin-right: 8px;
            color: #667eea;
        }

        .footer-bottom {
            border-top: 1px solid #34495e;
            padding-top: 20px;
            margin-top: 40px;
            text-align: center;
            color: #bdc3c7;
        }

        .footer-social {
            margin-top: 20px;
        }

        .footer-social a {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            margin: 0 5px;
            color: #bdc3c7;
            transition: all 0.3s ease;
        }

        .footer-social a:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-3px);
        }

        .footer-social-item {
            margin-bottom: 10px;
        }

        .facebook-link {
            display: inline-block;
            background-color: #3b5998;
            color: white !important;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .facebook-link:hover {
            background-color: #2d4373;
            transform: translateY(-2px);
            color: white !important;
        }

        .footer-link {
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-link:hover {
            color: #ecf0f1;
            text-decoration: underline;
        }

        /* Image Optimization Styles */
        .card-img-top {
            object-fit: cover;
            object-position: center top;
            transition: transform 0.3s ease;
        }

        /* สำหรับรูปภาพที่มีคน ให้โฟกัสที่ใบหน้า */
        .staff-avatar,
        .rounded-circle {
            object-fit: cover;
            object-position: center 20%;
        }

        /* สำหรับรูปข่าวและกิจกรรม */
        .news-card .card-img-top,
        .gallery-item img {
            object-fit: cover;
            object-position: center center;
        }

        /* ปรับปรุงการแสดงผลรูปภาพในการ์ด */
        .card img {
            border-radius: inherit;
        }

        /* เอฟเฟกต์ hover สำหรับรูปภาพ */
        .card:hover .card-img-top {
            transform: scale(1.02);
        }

        /* ปรับปรุงรูปภาพในแกลเลอรี่ */
        .gallery-item {
            aspect-ratio: 4/3;
            overflow: hidden;
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center center;
        }

        /* ปรับปรุงรูปภาพบุคลากร */
        .staff-card img {
            aspect-ratio: 1/1;
            object-fit: cover;
            object-position: center 25%;
        }

        /* สำหรับรูปภาพที่มีขนาดใหญ่ */
        .img-fluid {
            max-width: 100%;
            height: auto;
            object-fit: cover;
        }

        /* ปรับปรุงการแสดงผลใน modal */
        .modal-body img {
            object-fit: contain;
            object-position: center center;
            max-height: 70vh;
        }

        /* Responsive Footer */
        @media (max-width: 992px) {
            .footer-section {
                margin-bottom: 30px;
            }
        }

        @media (max-width: 768px) {
            .footer {
                padding: 35px 0 20px 0;
            }

            .footer-section {
                margin-bottom: 35px;
                text-align: center;
            }

            .footer-title {
                margin-bottom: 12px;
            }

            .footer-title::after {
                left: 50%;
                transform: translateX(-50%);
            }

            .footer-contact-item {
                justify-content: center;
                text-align: center;
                max-width: 320px;
                margin: 5px auto;
                padding: 1px 0;
            }

            .footer-social-item {
                text-align: center;
                margin-bottom: 8px;
            }

            .facebook-link {
                display: inline-block;
                margin: 0 auto;
            }

            .footer-bottom {
                text-align: center;
            }
        }

        @media (max-width: 576px) {
            .footer-title {
                font-size: 1.05rem;
                margin-bottom: 10px;
            }

            .footer-contact-item {
                font-size: 0.9rem;
                margin: 4px auto;
            }

            .facebook-link {
                font-size: 0.9rem;
                padding: 6px 12px;
            }

            .footer-section {
                margin-bottom: 25px;
            }
        }
        
        .news-card img {
            height: 200px;
            object-fit: cover;
        }
        
        .gallery-item img {
            height: 250px;
            object-fit: cover;
            width: 100%;
        }
        
        .staff-card {
            text-align: center;
        }
        
        .staff-card img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            margin-bottom: 15px;
        }
    </style>
    
    @yield('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <!-- Brand -->
            <a class="navbar-brand" href="{{ route('home') }}">
                <i class="fas fa-child"></i>
                <div class="brand-text">
                    <span class="brand-main">ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า</span>
                    <small class="brand-subtitle d-none d-lg-block">หลักสูตรการเรียนรู้ที่เหมาะสมกับวัยและพัฒนาการของเด็กเล็ก</small>
                </div>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                            <i class="fas fa-home me-1"></i>หน้าหลัก
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('news.*') ? 'active' : '' }}" href="{{ route('news.index') }}">
                            <i class="fas fa-newspaper me-1"></i>ข่าวประชาสัมพันธ์
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('staff.*') ? 'active' : '' }}" href="{{ route('staff.index') }}">
                            <i class="fas fa-users me-1"></i>ข้อมูลบุคลากร
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('gallery.*') ? 'active' : '' }}" href="{{ route('gallery.index') }}">
                            <i class="fas fa-images me-1"></i>รูปภาพกิจกรรม
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" href="{{ route('contact') }}">
                            <i class="fas fa-phone me-1"></i>ติดต่อเรา
                        </a>
                    </li>


                    @guest
                        <li class="nav-item">
                            <a class="nav-link login-btn" href="{{ route('login') }}">
                                <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ
                            </a>
                        </li>
                    @else
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle admin-dropdown" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-1"></i>{{ Auth::user()->name }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.dashboard') }}">
                                        <i class="fas fa-tachometer-alt me-2"></i>ระบบจัดการ
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('logout') }}"
                                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                                    </a>
                                </li>
                            </ul>
                        </li>
                    @endguest
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <!-- ที่ตั้งสำนักงาน -->
                <div class="col-lg-4 col-md-6 footer-section">
                    <h5 class="footer-title">ที่ตั้งสำนักงาน</h5>

                    <div class="footer-contact-item">
                        <span>ตำบลบุ่งคล้า อำเภอหล่มสัก จังหวัดเพชรบูรณ์ 67110</span>
                    </div>

                    <div class="footer-contact-item">
                        <span><strong>เวลาทำการ:</strong> จันทร์ - ศุกร์ 08.30 - 16.30 น.</span>
                    </div>
                </div>

                <!-- โทรศัพท์ / โทรสาร 
                <div class="col-lg-4 col-md-6 footer-section">
                    <h5 class="footer-title">โทรศัพท์</h5>

                    <div class="footer-contact-item">
                        <span>036-689758 / 036-689946</span>
                    </div>

                    <div class="footer-contact-item">
                        <a href="https://www.google.com/maps/place/%E0%B8%A8%E0%B8%B9%E0%B8%99%E0%B8%A2%E0%B9%8C%E0%B8%9E%E0%B8%B1%E0%B8%92%E0%B8%99%E0%B8%B2%E0%B9%80%E0%B8%94%E0%B9%87%E0%B8%81%E0%B9%80%E0%B8%A5%E0%B9%87%E0%B8%81%E0%B8%95%E0%B8%B3%E0%B8%9A%E0%B8%A5%E0%B8%9A%E0%B8%B8%E0%B9%88%E0%B8%87%E0%B8%84%E0%B8%A5%E0%B9%89/@16.6245728,101.1822655,3a,75y,72.9h,73.61t/data=!3m7!1e1!3m5!1sxmQF7Yyq1Or4dvDzdKpEtQ!2e0!6shttps:%2F%2Fstreetviewpixels-pa.googleapis.com%2Fv1%2Fthumbnail%3Fcb_client%3Dmaps_sv.tactile%26w%3D900%26h%3D600%26pitch%3D16.388059388903912%26panoid%3DxmQF7Yyq1Or4dvDzdKpEtQ%26yaw%3D72.89876858596685!7i16384!8i8192!4m14!1m7!3m6!1s0x3120499981848f1d:0x9f9e285ab0a6082d!2z4Lio4Li54LiZ4Lii4LmM4Lie4Lix4LiS4LiZ4Liy4LmA4LiU4LmH4LiB4LmA4Lil4LmH4LiB4LiV4Liz4Lia4Lil4Lia4Li44LmI4LiH4LiE4Lil4LmJ!8m2!3d16.6243153!4d101.1821753!16s%2Fg%2F11j1byny7w!3m5!1s0x3120499981848f1d:0x9f9e285ab0a6082d!8m2!3d16.6243153!4d101.1821753!16s%2Fg%2F11j1byny7w?hl=th&entry=ttu&g_ep=EgoyMDI1MDcxMy4wIKXMDSoASAFQAw%3D%3D" class="footer-link">Sitemap</a>
                    </div>
                </div>-->

                <!-- SOCIAL NETWORK -->
                <div class="col-lg-4 col-md-6 footer-section">
                    <h5 class="footer-title">SOCIAL NETWORK</h5>

                    <div class="footer-social-item">
                        <a href="https://www.facebook.com/suny.phat.hna.dek.lek.t.bung.khla" class="facebook-link">
                            <i class="fab fa-facebook-f me-2"></i>facebook
                        </a>
                    </div>

                      <!--<div class="footer-contact-item">
                        <span><strong>E-Mail:</strong> <EMAIL></span>
                    </div> -->
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="text-center">
                    <p class="mb-0">
                        &copy; {{ date('Y') }} ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า สงวนลิขสิทธิ์
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Logout Form -->
    @auth
    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
        @csrf
    </form>
    @endauth

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Image Optimization JS -->
    <script src="{{ asset('js/image-optimization.js') }}"></script>
    <!-- Force Normal Colors JS -->
    <script src="{{ asset('js/force-normal-colors.js') }}"></script>

    <!-- Mobile menu auto-close -->
    <script>
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function() {
                const navbarCollapse = document.querySelector('.navbar-collapse');
                if (navbarCollapse.classList.contains('show')) {
                    const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                    bsCollapse.hide();
                }
            });
        });
    </script>

    @yield('scripts')
</body>
</html>
