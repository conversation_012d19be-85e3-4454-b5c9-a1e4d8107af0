@extends('layouts.admin')

@section('title', 'จัดการรูปภาพข่าว - ระบบจัดการ')
@section('page-title', 'จัดการรูปภาพข่าว: ' . $news->title)

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-images me-2"></i>รูปภาพข่าว: {{ $news->title }}
                </h5>
                <div>
                    <a href="{{ route('admin.news.show', $news->id) }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-eye me-1"></i>ดูข่าว
                    </a>
                    <a href="{{ route('admin.news.index') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>กลับ
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- ฟอร์มอัพโหลดรูปภาพใหม่ -->
                <div class="mb-4">
                    <h6 class="fw-bold mb-3">อัพโหลดรูปภาพใหม่</h6>
                    <form action="{{ route('admin.news.update', $news->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <!-- ซ่อนข้อมูลข่าวเดิม -->
                        <input type="hidden" name="title" value="{{ $news->title }}">
                        <input type="hidden" name="content" value="{{ $news->content }}">

                        @if($news->is_published)
                            <input type="hidden" name="is_published" value="1">
                        @endif
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="images" class="form-label">เลือกรูปภาพ</label>
                                    <input type="file"
                                           class="form-control @error('images') is-invalid @enderror"
                                           id="images"
                                           name="images[]"
                                           accept="image/*"
                                           multiple
                                           onchange="previewMultipleImages(this)">
                                    @error('images')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 5MB ต่อรูป, สูงสุด 10 รูป)</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-1"></i>อัพโหลด
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Preview รูปภาพที่เลือก -->
                        <div id="multipleImagesPreview" class="mt-3" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปภาพที่เลือก:</label>
                            <div id="previewContainer" class="row g-2"></div>
                        </div>
                    </form>
                </div>

                <hr>

                <!-- รูปภาพปัจจุบัน -->
                <h6 class="fw-bold mb-3">รูปภาพปัจจุบัน ({{ $news->images->count() }} รูป)</h6>
                
                @if($news->images->count() > 0)
                    <div class="row g-3">
                        @foreach($news->images as $image)
                        <div class="col-md-3 col-sm-4 col-6">
                            <div class="card">
                                <img src="{{ $image->thumbnail_url ?: $image->image_url }}"
                                     class="card-img-top"
                                     style="height: 150px; object-fit: cover;"
                                     alt="รูปภาพ {{ $loop->iteration }}">
                                <div class="card-body p-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            รูปที่ {{ $image->sort_order + 1 }}
                                            @if($image->is_featured)
                                                <span class="badge bg-primary ms-1">หลัก</span>
                                            @endif
                                        </small>
                                        <div class="btn-group btn-group-sm">
                                            @if(!$image->is_featured)
                                                <button type="button" 
                                                        class="btn btn-outline-primary btn-sm"
                                                        onclick="setFeatured({{ $image->id }})"
                                                        title="ตั้งเป็นรูปหลัก">
                                                    <i class="fas fa-star"></i>
                                                </button>
                                            @endif
                                            <button type="button" 
                                                    class="btn btn-outline-danger btn-sm"
                                                    onclick="deleteImage({{ $image->id }})"
                                                    title="ลบรูปภาพ">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                        <p class="text-muted">ยังไม่มีรูปภาพ</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// ฟังก์ชันสำหรับแสดงตัวอย่างรูปภาพหลายรูป
function previewMultipleImages(input) {
    const previewContainer = document.getElementById('multipleImagesPreview');
    const imageContainer = document.getElementById('previewContainer');

    // ล้างรูปภาพเก่า
    imageContainer.innerHTML = '';

    if (input.files && input.files.length > 0) {
        // ตรวจสอบจำนวนรูปภาพ
        if (input.files.length > 10) {
            alert('สามารถเลือกได้สูงสุด 10 รูปเท่านั้น');
            input.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        previewContainer.style.display = 'block';

        // แสดงตัวอย่างแต่ละรูป
        Array.from(input.files).forEach((file, index) => {
            // ตรวจสอบขนาดไฟล์
            if (file.size > 5 * 1024 * 1024) { // 5MB
                alert(`รูปภาพ "${file.name}" มีขนาดใหญ่เกิน 5MB`);
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const col = document.createElement('div');
                col.className = 'col-md-2 col-sm-3 col-4';
                col.innerHTML = `
                    <div class="card">
                        <img src="${e.target.result}" class="card-img-top" style="height: 100px; object-fit: cover;" alt="Preview ${index + 1}">
                        <div class="card-body p-1">
                            <small class="text-muted">รูปที่ ${index + 1}</small>
                        </div>
                    </div>
                `;
                imageContainer.appendChild(col);
            };
            reader.readAsDataURL(file);
        });
    } else {
        previewContainer.style.display = 'none';
    }
}

// ฟังก์ชันลบรูปภาพ
function deleteImage(imageId) {
    if (confirm('คุณต้องการลบรูปภาพนี้หรือไม่?')) {
        fetch(`{{ url('admin/news/images') }}/${imageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการลบรูปภาพ');
        });
    }
}

// ฟังก์ชันตั้งรูปหลัก
function setFeatured(imageId) {
    if (confirm('ตั้งรูปภาพนี้เป็นรูปหลักหรือไม่?')) {
        fetch(`/admin/news/images/${imageId}/featured`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการตั้งรูปหลัก');
        });
    }
}
</script>
@endsection
