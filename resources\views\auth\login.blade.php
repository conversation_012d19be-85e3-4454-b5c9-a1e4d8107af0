@extends('layouts.app')

@section('title', 'เข้าสู่ระบบ - ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า')

@section('styles')
<style>
    .login-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
    }

    .login-card {
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border-radius: 15px;
        overflow: hidden;
    }

    .login-header {
        background: #3498db;
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .login-body {
        padding: 2rem;
    }

    .form-control {
        border-radius: 10px;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-login {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
        border: none !important;
        border-radius: 15px !important;
        padding: 15px 40px !important;
        font-weight: 700 !important;
        font-size: 1.1rem !important;
        text-transform: uppercase !important;
        letter-spacing: 1.5px !important;
        transition: all 0.4s ease !important;
        color: white !important;
        box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4) !important;
        position: relative !important;
        overflow: hidden !important;
        width: 100% !important;
    }

    .btn-login:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s;
    }

    .btn-login:hover:before {
        left: 100%;
    }

    .btn-login:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%) !important;
        transform: translateY(-4px) scale(1.02) !important;
        box-shadow: 0 10px 30px rgba(30, 64, 175, 0.6) !important;
        color: white !important;
    }

    .btn-login:active {
        transform: translateY(-2px) scale(1.01) !important;
    }

    .btn-login i {
        margin-right: 10px !important;
        font-size: 1.2rem !important;
    }

    /* Pulse Animation for Login Button */
    @keyframes loginPulse {
        0% {
            box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4);
        }
        50% {
            box-shadow: 0 6px 25px rgba(30, 64, 175, 0.6);
        }
        100% {
            box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4);
        }
    }

    .btn-login {
        animation: loginPulse 2s ease-in-out infinite;
    }

    .btn-login:hover {
        animation: none !important;
    }
</style>
@endsection

@section('content')
<div class="container login-container">
    <div class="row justify-content-center w-100">
        <div class="col-md-6 col-lg-5">
            <div class="card login-card">
                <div class="login-header">
                    <i class="fas fa-child fa-3x mb-3"></i>
                    <h4 class="mb-2">เข้าสู่ระบบจัดการ</h4>
                    <p class="mb-0">ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า</p>
                </div>

                <div class="login-body">
                    <form method="POST" action="{{ route('login') }}">
                        @csrf

                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>อีเมล
                            </label>
                            <input id="email"
                                   type="email"
                                   class="form-control @error('email') is-invalid @enderror"
                                   name="email"
                                   value="{{ old('email') }}"
                                   required
                                   autocomplete="email"
                                   autofocus
                                   placeholder="กรุณากรอกอีเมล">

                            @error('email')
                                <div class="invalid-feedback">
                                    <strong>{{ $message }}</strong>
                                </div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>รหัสผ่าน
                            </label>
                            <input id="password"
                                   type="password"
                                   class="form-control @error('password') is-invalid @enderror"
                                   name="password"
                                   required
                                   autocomplete="current-password"
                                   placeholder="กรุณากรอกรหัสผ่าน">

                            @error('password')
                                <div class="invalid-feedback">
                                    <strong>{{ $message }}</strong>
                                </div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input"
                                       type="checkbox"
                                       name="remember"
                                       id="remember"
                                       {{ old('remember') ? 'checked' : '' }}>
                                <label class="form-check-label" for="remember">
                                    จดจำการเข้าสู่ระบบ
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-login">
                                <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                            </button>
                        </div>

                        <div class="text-center mt-3">
                            <a href="{{ route('home') }}" class="text-muted text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>กลับสู่หน้าหลัก
                            </a>
                        </div>
                    </form>

                    <!-- Demo Credentials 
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-info-circle me-2"></i>ข้อมูลสำหรับทดสอบ:
                        </h6>
                        <small class="text-muted">
                            <strong>Email:</strong> <EMAIL><br>
                            <strong>Password:</strong> password
                        </small>
                    </div>-->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
