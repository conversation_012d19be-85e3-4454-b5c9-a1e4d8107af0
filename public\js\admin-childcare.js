/**
 * JavaScript สำหรับระบบหลังบ้าน ศูนย์พัฒนาเด็กเล็ก
 * Child Care Center Admin Dashboard JavaScript
 * ออกแบบใหม่ - เพิ่มเอฟเฟกต์และ Animation ที่สวยงาม
 */

document.addEventListener('DOMContentLoaded', function() {

    // ========== การเริ่มต้น ==========
    initializeAdminDashboard();

    // ========== ฟังก์ชันหลัก ==========
    function initializeAdminDashboard() {
        addFormValidations();
        addNotificationSystem();
        addMobileMenuToggle();
        addLoadingStates();
        // All animations and effects removed
    }
    
    // ========== Stats Cards (animations removed) ==========
    // All card animations and hover effects removed

    // ========== Counter Animation (removed) ==========
    // All counter animations removed

    // Counter animation code removed
    
    // ========== Button Effects (removed) ==========
    // All button effects removed
    
    // ========== Table Enhancements (removed) ==========
    // All table hover effects removed
    // Table effects code removed
    
    // ========== Form Validations ========== 
    function addFormValidations() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });
            });
        });
    }
    
    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        
        if (isRequired && !value) {
            showFieldError(field, 'กรุณากรอกข้อมูลในช่องนี้');
            return false;
        }
        
        clearFieldError(field);
        return true;
    }
    
    function showFieldError(field, message) {
        clearFieldError(field);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error text-danger small mt-1';
        errorDiv.textContent = message;
        
        field.parentElement.appendChild(errorDiv);
        field.classList.add('is-invalid');
    }
    
    function clearFieldError(field) {
        const existingError = field.parentElement.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        field.classList.remove('is-invalid');
    }
    
    // ========== Notification System ========== 
    function addNotificationSystem() {
        // สร้าง container สำหรับ notifications
        if (!document.querySelector('.notification-container')) {
            const container = document.createElement('div');
            container.className = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
    }
    
    // ========== Mobile Menu Toggle ========== 
    function addMobileMenuToggle() {
        // สร้างปุ่ม toggle สำหรับ mobile
        if (window.innerWidth <= 768) {
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'btn btn-primary d-md-none position-fixed';
            toggleBtn.style.cssText = `
                top: 10px;
                left: 10px;
                z-index: 1001;
                border-radius: 50%;
                width: 50px;
                height: 50px;
            `;
            toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
            
            toggleBtn.addEventListener('click', function() {
                const sidebar = document.querySelector('.sidebar');
                sidebar.classList.toggle('show');
            });
            
            document.body.appendChild(toggleBtn);
        }
    }
    
    // ========== Loading States ========== 
    function addLoadingStates() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังบันทึก...';
                    submitBtn.disabled = true;
                    
                    // คืนค่าปุ่มหลังจาก 3 วินาที (ในกรณีที่ไม่มีการ redirect)
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 3000);
                }
            });
        });
    }
    
    // ========== Utility Functions ========== 
    window.showNotification = function(message, type = 'success') {
        const container = document.querySelector('.notification-container');
        const notification = document.createElement('div');
        
        const bgColor = type === 'success' ? 'var(--admin-secondary)' : 
                       type === 'error' ? 'var(--admin-primary)' : 
                       'var(--admin-accent)';
        
        notification.className = 'notification mb-3';
        notification.style.cssText = `
            background: ${bgColor};
            color: white;
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        notification.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>${message}</span>
                <button class="btn-close btn-close-white ms-3" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        
        container.appendChild(notification);
        
        // แสดง notification
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // ซ่อน notification หลังจาก 5 วินาที
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    };
    
    window.confirmDelete = function(message = 'คุณแน่ใจหรือไม่ที่จะลบข้อมูลนี้?') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content" style="border-radius: 20px;">
                        <div class="modal-header" style="background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary)); color: white; border-radius: 20px 20px 0 0;">
                            <h5 class="modal-title">⚠️ ยืนยันการลบ</h5>
                        </div>
                        <div class="modal-body text-center py-4">
                            <p class="mb-0">${message}</p>
                        </div>
                        <div class="modal-footer justify-content-center">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                            <button type="button" class="btn btn-danger confirm-delete">ลบข้อมูล</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
            
            modal.querySelector('.confirm-delete').addEventListener('click', () => {
                resolve(true);
                bsModal.hide();
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
                resolve(false);
            });
        });
    };
    
});

// ========== All CSS animations and special effects removed ==========
// All ripple effects, sparkle effects, floating hearts, and shine effects have been removed
