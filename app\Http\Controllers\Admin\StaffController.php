<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Staff;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class StaffController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $staff = Staff::ordered()->paginate(10);
        return view('admin.staff.index', compact('staff'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.staff.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean'
        ]);

        $data = $request->all();
        $data['is_active'] = $request->has('is_active');

        if ($request->hasFile('photo')) {
            $data['photo'] = $request->file('photo')->store('staff', 'public');
            // ซิงค์ไฟล์ไปยัง public/storage
            $this->syncImageToPublic($data['photo']);
        }

        Staff::create($data);

        return redirect()->route('admin.staff.index')
            ->with('success', 'ข้อมูลบุคลากรถูกเพิ่มเรียบร้อยแล้ว');
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $staff = Staff::findOrFail($id);
        return view('admin.staff.show', compact('staff'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $staff = Staff::findOrFail($id);
        return view('admin.staff.edit', compact('staff'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $staff = Staff::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean'
        ]);

        $data = $request->all();
        $data['is_active'] = $request->has('is_active');

        if ($request->hasFile('photo')) {
            // ลบรูปเก่า
            if ($staff->photo) {
                Storage::disk('public')->delete($staff->photo);
                // ลบไฟล์ใน public/storage ด้วย
                $oldPhotoPath = public_path('storage/' . str_replace('\\', '/', $staff->photo));
                if (file_exists($oldPhotoPath)) {
                    unlink($oldPhotoPath);
                }
            }
            $data['photo'] = $request->file('photo')->store('staff', 'public');
            // ซิงค์ไฟล์ไปยัง public/storage
            $this->syncImageToPublic($data['photo']);
        }

        $staff->update($data);

        return redirect()->route('admin.staff.index')
            ->with('success', 'ข้อมูลบุคลากรถูกอัพเดทเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        \Log::info('StaffController::destroy called with ID: ' . $id);

        try {
            $staff = Staff::findOrFail($id);
            \Log::info('Found staff: ' . $staff->id . ', name: ' . $staff->name);

            // ลบรูปภาพ
            if ($staff->photo && Storage::disk('public')->exists($staff->photo)) {
                $deleted = Storage::disk('public')->delete($staff->photo);
                \Log::info('Deleted staff photo: ' . ($deleted ? 'success' : 'failed'));
            }

            $staff->delete();
            \Log::info('Deleted staff record from database');

            return redirect()->route('admin.staff.index')
                ->with('success', 'ข้อมูลบุคลากรถูกลบเรียบร้อยแล้ว');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Log::error('Staff not found: ' . $id);
            return redirect()->route('admin.staff.index')
                ->with('error', 'ไม่พบข้อมูลบุคลากรที่ต้องการลบ');
        } catch (\Exception $e) {
            \Log::error('Error deleting staff: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());
            return redirect()->route('admin.staff.index')
                ->with('error', 'เกิดข้อผิดพลาดในการลบข้อมูลบุคลากร: ' . $e->getMessage());
        }
    }

    /**
     * Toggle status of the specified resource.
     */
    public function toggleStatus($id)
    {
        try {
            $staff = Staff::findOrFail($id);
            $staff->is_active = !$staff->is_active;
            $staff->save();

            return response()->json([
                'success' => true,
                'is_active' => $staff->is_active,
                'message' => $staff->is_active ? 'เปิดใช้งานบุคลากรเรียบร้อยแล้ว' : 'ปิดใช้งานบุคลากรเรียบร้อยแล้ว'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการอัพเดทสถานะ'
            ], 500);
        }
    }

    /**
     * ซิงค์ไฟล์จาก storage/app/public ไปยัง public/storage
     */
    private function syncImageToPublic($imagePath)
    {
        if (!$imagePath) {
            return false;
        }

        $sourcePath = storage_path('app/public/' . str_replace('\\', '/', $imagePath));
        $targetPath = public_path('storage/' . str_replace('\\', '/', $imagePath));

        // สร้างโฟลเดอร์ target หากไม่มี
        $targetDir = dirname($targetPath);
        if (!file_exists($targetDir)) {
            mkdir($targetDir, 0755, true);
        }

        // คัดลอกไฟล์
        if (file_exists($sourcePath) && !file_exists($targetPath)) {
            copy($sourcePath, $targetPath);
            return true;
        }

        return false;
    }
}
