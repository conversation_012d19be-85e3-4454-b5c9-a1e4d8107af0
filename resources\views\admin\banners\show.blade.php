@extends('layouts.admin')

@section('title', 'ดู Banner')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ดู Banner: {{ $banner->title }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.banners.edit', $banner) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> แก้ไข
                        </a>
                        <a href="{{ route('admin.banners.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> กลับ
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Banner Preview -->
                            <div class="mb-4">
                                <h5>ตัวอย่าง Banner</h5>
                                <div class="banner-preview" style="position: relative; height: 400px; overflow: hidden; border-radius: 10px;">
                                    <img src="{{ $banner->image_url }}" 
                                         alt="{{ $banner->title }}" 
                                         style="width: 100%; height: 100%; object-fit: cover;">
                                    
                                    <!-- Banner Overlay -->
                                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; 
                                                background: linear-gradient(135deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 100%);
                                                display: flex; align-items: center;">
                                        <div class="container">
                                            <div class="row">
                                                <div class="col-lg-8">
                                                    <div style="color: white;">
                                                        @if($banner->subtitle)
                                                            <div class="badge bg-light text-primary mb-3 px-3 py-2">
                                                                <i class="fas fa-star me-2"></i>{{ $banner->subtitle }}
                                                            </div>
                                                        @endif
                                                        
                                                        <h1 class="display-4 fw-bold mb-4">{{ $banner->title }}</h1>
                                                        
                                                        @if($banner->description)
                                                            <p class="lead mb-4">{{ $banner->description }}</p>
                                                        @endif
                                                        
                                                        @if($banner->button_text && $banner->button_url)
                                                            <a href="{{ $banner->button_url }}" 
                                                               class="btn {{ $banner->button_class }} btn-lg px-4"
                                                               target="_blank">
                                                                {{ $banner->button_text }}
                                                            </a>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Banner Details -->
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>รายละเอียด Banner</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="120"><strong>ชื่อ Banner:</strong></td>
                                            <td>{{ $banner->title }}</td>
                                        </tr>
                                        @if($banner->subtitle)
                                        <tr>
                                            <td><strong>หัวข้อย่อย:</strong></td>
                                            <td>{{ $banner->subtitle }}</td>
                                        </tr>
                                        @endif
                                        @if($banner->description)
                                        <tr>
                                            <td><strong>คำอธิบาย:</strong></td>
                                            <td>{{ $banner->description }}</td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td><strong>ลำดับการแสดง:</strong></td>
                                            <td><span class="badge bg-secondary">{{ $banner->sort_order }}</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>สถานะ:</strong></td>
                                            <td>
                                                @if($banner->is_active)
                                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                                @else
                                                    <span class="badge bg-secondary">ปิดใช้งาน</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>สร้างเมื่อ:</strong></td>
                                            <td>{{ $banner->created_at->format('d/m/Y H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>แก้ไขล่าสุด:</strong></td>
                                            <td>{{ $banner->updated_at->format('d/m/Y H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>

                                <div class="col-md-6">
                                    <h5>การตั้งค่าปุ่ม</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td width="120"><strong>ข้อความปุ่ม:</strong></td>
                                            <td>{{ $banner->button_text ?: '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>ลิงก์ปุ่ม:</strong></td>
                                            <td>
                                                @if($banner->button_url)
                                                    <a href="{{ $banner->button_url }}" target="_blank" class="text-break">
                                                        {{ $banner->button_url }}
                                                        <i class="fas fa-external-link-alt ms-1"></i>
                                                    </a>
                                                @else
                                                    -
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>สีปุ่ม:</strong></td>
                                            <td>
                                                <span class="btn btn-sm {{ $banner->button_class }}">
                                                    {{ ucfirst($banner->button_color) }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>

                                    @if($banner->button_text && $banner->button_url)
                                        <h6>ตัวอย่างปุ่ม:</h6>
                                        <a href="{{ $banner->button_url }}" 
                                           class="btn {{ $banner->button_class }}" 
                                           target="_blank">
                                            {{ $banner->button_text }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Image Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">ข้อมูลรูปภาพ</h5>
                                </div>
                                <div class="card-body">
                                    <img src="{{ $banner->image_url }}" 
                                         alt="{{ $banner->title }}" 
                                         class="img-fluid rounded mb-3">
                                    
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>ไฟล์:</strong></td>
                                            <td>{{ basename($banner->image_path) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>URL:</strong></td>
                                            <td>
                                                <a href="{{ $banner->image_url }}" target="_blank" class="text-break">
                                                    ดูรูปภาพ <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">การจัดการ</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('admin.banners.edit', $banner) }}" class="btn btn-warning">
                                            <i class="fas fa-edit"></i> แก้ไข Banner
                                        </a>

                                        <button type="button"
                                                class="btn btn-danger"
                                                onclick="deleteBanner({{ $banner->id }})">
                                            <i class="fas fa-trash"></i> ลบ Banner
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ยืนยันการลบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>คุณแน่ใจหรือไม่ที่จะลบ Banner "<strong>{{ $banner->title }}</strong>"?</p>
                <p class="text-danger"><small>การดำเนินการนี้ไม่สามารถย้อนกลับได้</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <form id="deleteForm" action="{{ route('admin.banners.destroy', $banner) }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>ลบ
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>


function deleteBanner(id) {
    if (confirm('คุณต้องการลบ Banner นี้หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้')) {
        // สร้าง form แบบ dynamic
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/banners/' + id;

        // เพิ่ม CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // เพิ่ม method DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // เพิ่ม form ไปยัง body และ submit
        document.body.appendChild(form);
        form.submit();
    }
}


</script>
@endsection
