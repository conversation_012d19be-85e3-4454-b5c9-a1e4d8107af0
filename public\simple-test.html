<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบง่าย</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        .test-card {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">ทดสอบเว็บไซต์ง่าย</h1>
        
        <div class="test-card">
            <h3>ทดสอบปุ่ม</h3>
            <button class="btn btn-primary me-2" onclick="showMessage('ปุ่มหลักทำงาน!')">ปุ่มหลัก</button>
            <button class="btn btn-success me-2" onclick="showMessage('ปุ่มสำเร็จทำงาน!')">ปุ่มสำเร็จ</button>
            <button class="btn btn-warning me-2" onclick="showMessage('ปุ่มเตือนทำงาน!')">ปุ่มเตือน</button>
        </div>
        
        <div class="test-card">
            <h3>ทดสอบลิงก์</h3>
            <a href="#" class="btn btn-outline-primary me-2" onclick="showMessage('ลิงก์ 1 ทำงาน!'); return false;">ลิงก์ 1</a>
            <a href="#" class="btn btn-outline-secondary me-2" onclick="showMessage('ลิงก์ 2 ทำงาน!'); return false;">ลิงก์ 2</a>
            <a href="#" class="btn btn-outline-info me-2" onclick="showMessage('ลิงก์ 3 ทำงาน!'); return false;">ลิงก์ 3</a>
        </div>
        
        <div class="test-card">
            <h3>ทดสอบ Navigation</h3>
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#" onclick="showMessage('Brand คลิกได้!'); return false;">Brand</a>
                    <div class="navbar-nav">
                        <a class="nav-link" href="#" onclick="showMessage('หน้าหลักคลิกได้!'); return false;">หน้าหลัก</a>
                        <a class="nav-link" href="#" onclick="showMessage('ข่าวสารคลิกได้!'); return false;">ข่าวสาร</a>
                        <a class="nav-link" href="#" onclick="showMessage('ติดต่อคลิกได้!'); return false;">ติดต่อ</a>
                    </div>
                </div>
            </nav>
        </div>
        
        <div class="test-card">
            <h3>ทดสอบรูปภาพ</h3>
            <img src="https://via.placeholder.com/200x150/007bff/ffffff?text=Test+Image" 
                 class="img-fluid" 
                 alt="รูปทดสอบ"
                 onclick="showMessage('รูปภาพคลิกได้!')"
                 style="cursor: pointer; max-width: 200px;">
        </div>
        
        <div id="message" class="success-message">
            <strong>ผลลัพธ์:</strong> <span id="message-text"></span>
        </div>
        
        <div class="test-card">
            <h3>ข้อมูลระบบ</h3>
            <p><strong>เวลาโหลดหน้า:</strong> <span id="load-time"></span></p>
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>ขนาดหน้าจอ:</strong> <span id="screen-size"></span></p>
        </div>
        
        <div class="test-card">
            <h3>ลิงก์ไปหน้าหลัก</h3>
            <a href="/" class="btn btn-lg btn-primary">กลับไปหน้าหลัก</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const startTime = performance.now();
        
        function showMessage(text) {
            const messageDiv = document.getElementById('message');
            const messageText = document.getElementById('message-text');
            
            messageText.textContent = text + ' เวลา: ' + new Date().toLocaleTimeString();
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 3000);
            
            console.log('Click: ' + text);
        }
        
        window.addEventListener('load', function() {
            const loadTime = (performance.now() - startTime).toFixed(2);
            document.getElementById('load-time').textContent = loadTime + ' ms';
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('screen-size').textContent = screen.width + 'x' + screen.height;
            
            console.log('Page loaded in ' + loadTime + ' ms');
        });
        
        // ทดสอบ console
        console.log('Simple test page initialized');
    </script>
</body>
</html>
