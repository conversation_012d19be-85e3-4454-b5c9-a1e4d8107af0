# 📸 คู่มือการใช้งานระบบอัปโหลดรูปภาพหลายรูปสำหรับข่าวสาร

## ✨ ฟีเจอร์ใหม่ที่เพิ่มเข้ามา

### 🔧 การปรับปรุงหน้าเพิ่มข่าวสาร (Create)
- **รูปภาพหลัก**: อัปโหลดรูปภาพหลักได้ 1 รูป (ขนาดไม่เกิน 2MB)
- **รูปภาพเพิ่มเติม**: อัปโหลดรูปภาพเพิ่มเติมได้สูงสุด 10 รูป (ขนาดไม่เกิน 5MB ต่อรูป)
- **ตัวอย่างรูปภาพ**: แสดงตัวอย่างรูปภาพทั้งหมดที่เลือกก่อนบันทึก
- **ตรวจสอบขนาดไฟล์**: แสดงขนาดไฟล์และตรวจสอบขนาดอัตโนมัติ

### 🔧 การปรับปรุงหน้าแก้ไขข่าวสาร (Edit)
- **แสดงรูปภาพปัจจุบัน**: แสดงรูปภาพหลักและรูปภาพเพิ่มเติมที่มีอยู่แล้ว
- **ลบรูปภาพ**: สามารถลบรูปภาพเพิ่มเติมแต่ละรูปได้
- **เพิ่มรูปภาพใหม่**: เพิ่มรูปภาพเพิ่มเติมใหม่ได้
- **เปลี่ยนรูปภาพหลัก**: เปลี่ยนรูปภาพหลักได้

## 🗂️ โครงสร้างฐานข้อมูล

### ตาราง `news`
- `id` - รหัสข่าว
- `title` - หัวข้อข่าว
- `content` - เนื้อหาข่าว
- `image` - รูปภาพหลัก (เพื่อ backward compatibility)
- `published_date` - วันที่เผยแพร่
- `is_published` - สถานะการเผยแพร่
- `created_by` - ผู้สร้าง

### ตาราง `news_images`
- `id` - รหัสรูปภาพ
- `news_id` - รหัสข่าว (Foreign Key)
- `image_path` - ที่อยู่ไฟล์รูปภาพ
- `thumbnail_path` - ที่อยู่ไฟล์ thumbnail
- `alt_text` - ข้อความอธิบายรูปภาพ
- `sort_order` - ลำดับการแสดงผล
- `is_featured` - รูปภาพหลัก

## 📁 โครงสร้างไฟล์

```
storage/app/public/
├── news/
│   ├── images/           # รูปภาพต้นฉบับ
│   └── thumbnails/       # รูปภาพ thumbnail
```

## 🎯 วิธีการใช้งาน

### การเพิ่มข่าวสารใหม่
1. เข้าสู่ระบบ Admin: `http://localhost:8000/login`
2. ไปที่เมนู "จัดการข่าวสาร" → "เพิ่มข่าวสาร"
3. กรอกข้อมูลข่าวสาร
4. **รูปภาพหลัก**: เลือกรูปภาพหลัก 1 รูป
5. **รูปภาพเพิ่มเติม**: เลือกรูปภาพเพิ่มเติมได้หลายรูป (สูงสุด 10 รูป)
6. ระบบจะแสดงตัวอย่างรูปภาพทั้งหมด
7. คลิก "บันทึก"

### การแก้ไขข่าวสาร
1. ไปที่รายการข่าวสาร → คลิก "แก้ไข"
2. ระบบจะแสดงรูปภาพที่มีอยู่แล้ว
3. สามารถลบรูปภาพเพิ่มเติมได้โดยคลิกปุ่ม "ลบ"
4. สามารถเพิ่มรูปภาพใหม่ได้
5. คลิก "บันทึกการแก้ไข"

## 🔧 ฟีเจอร์เทคนิค

### JavaScript Functions
- `previewSingleImage()` - แสดงตัวอย่างรูปภาพหลัก
- `previewMultipleImages()` - แสดงตัวอย่างรูปภาพหลายรูป
- `deleteImage()` - ลบรูปภาพผ่าน AJAX
- `formatFileSize()` - แปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย

### Laravel Features
- **Image Processing**: ใช้ Intervention Image สำหรับสร้าง thumbnail
- **File Validation**: ตรวจสอบประเภทและขนาดไฟล์
- **Storage Management**: จัดการไฟล์ผ่าน Laravel Storage
- **AJAX Deletion**: ลบรูปภาพแบบ real-time

## 🚀 การปรับปรุงที่ทำ

### 1. ไฟล์ที่แก้ไข
- `resources/views/admin/news/create.blade.php` - เพิ่มฟอร์มอัปโหลดหลายรูป
- `resources/views/admin/news/edit.blade.php` - เพิ่มการจัดการรูปภาพที่มีอยู่
- `app/Http/Controllers/Admin/NewsController.php` - เพิ่ม method deleteImage
- `routes/web.php` - เพิ่ม route สำหรับลบรูปภาพ
- `resources/views/layouts/admin.blade.php` - เพิ่ม CSRF token

### 2. ฟีเจอร์ที่เพิ่ม
- ✅ อัปโหลดรูปภาพหลายรูปพร้อมกัน
- ✅ แสดงตัวอย่างรูปภาพก่อนบันทึก
- ✅ ตรวจสอบขนาดและจำนวนไฟล์
- ✅ ลบรูปภาพแต่ละรูปได้
- ✅ แสดงข้อมูลขนาดไฟล์
- ✅ รองรับ backward compatibility

## 📋 ข้อกำหนดระบบ

### ไฟล์ที่รองรับ
- **ประเภท**: JPG, PNG, GIF
- **ขนาดรูปหลัก**: ไม่เกิน 2MB
- **ขนาดรูปเพิ่มเติม**: ไม่เกิน 5MB ต่อรูป
- **จำนวนรูปเพิ่มเติม**: สูงสุด 10 รูป

### ความต้องการเซิร์ฟเวอร์
- PHP 8.0+
- Laravel 10+
- GD Extension หรือ ImageMagick
- Storage permission สำหรับโฟลเดอร์ storage/app/public

## 🎉 สรุป

ระบบอัปโหลดรูปภาพหลายรูปสำหรับข่าวสารได้รับการปรับปรุงเรียบร้อยแล้ว! 
ตอนนี้ผู้ดูแลระบบสามารถ:
- อัปโหลดรูปภาพหลายรูปพร้อมกัน
- ดูตัวอย่างรูปภาพก่อนบันทึก
- จัดการรูปภาพที่มีอยู่แล้ว
- ลบรูปภาพแต่ละรูปได้

ระบบยังคงรองรับการทำงานแบบเดิม (รูปเดียว) เพื่อความเข้ากันได้ย้อนหลัง
