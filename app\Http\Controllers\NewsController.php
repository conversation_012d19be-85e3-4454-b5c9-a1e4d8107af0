<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    public function index()
    {
        $news = News::published()
            ->with(['images', 'featuredImage'])
            ->latest()
            ->paginate(10);

        return view('news.index', compact('news'));
    }

    public function show($id)
    {
        $newsItem = News::published()
            ->with(['images', 'featuredImage'])
            ->findOrFail($id);

        // ดึงข่าวที่เกี่ยวข้อง
        $relatedNews = News::published()
            ->where('id', '!=', $newsItem->id)
            ->latest()
            ->take(5)
            ->get(['id', 'title', 'created_at']);

        return view('news.show', compact('newsItem', 'relatedNews'));
    }
}
