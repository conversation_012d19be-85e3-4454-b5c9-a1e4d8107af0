/**
 * Force Normal Colors JavaScript
 * ให้แน่ใจว่ารูปภาพทั้งหมดแสดงเป็นสีปกติ
 */

// เรียกใช้ทันทีที่ script โหลด (ก่อน DOMContentLoaded)
(function() {
    'use strict';
    
    /**
     * Force normal colors for all images immediately
     */
    function forceNormalColorsImmediate() {
        const images = document.querySelectorAll('img');
        images.forEach(forceImageNormalColor);
    }
    
    /**
     * Force normal color for a single image
     */
    function forceImageNormalColor(img) {
        if (!img) return;
        
        // ลบ filter ทั้งหมด
        img.style.filter = 'none';
        img.style.webkitFilter = 'none';
        img.style.mozFilter = 'none';
        img.style.msFilter = 'none';
        img.style.oFilter = 'none';
        
        // ให้แน่ใจว่า opacity เป็น 1 (ยกเว้นกรณีที่กำลังโหลด)
        if (!img.classList.contains('image-loading') && 
            !img.classList.contains('lazy') &&
            !img.hasAttribute('data-src')) {
            img.style.opacity = '1';
        }
        
        // ลบ effects ที่อาจทำให้สีเปลี่ยน
        img.style.mixBlendMode = 'normal';
        img.style.isolation = 'auto';
        
        // ให้แน่ใจว่าการแสดงผลสีถูกต้อง
        img.style.colorAdjust = 'exact';
        img.style.webkitPrintColorAdjust = 'exact';
        img.style.printColorAdjust = 'exact';
        
        // ลบ transform ที่อาจส่งผลต่อสี
        if (img.style.transform && img.style.transform.includes('filter')) {
            img.style.transform = img.style.transform.replace(/filter\([^)]*\)/g, '');
        }
        
        // เพิ่ม class เพื่อระบุว่าได้แก้ไขแล้ว
        img.classList.add('force-normal-color');
    }
    
    /**
     * Setup mutation observer to handle dynamically added images
     */
    function setupMutationObserver() {
        if (typeof MutationObserver === 'undefined') return;
        
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // ตรวจสอบ nodes ที่เพิ่มเข้ามา
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        // ถ้าเป็น img element
                        if (node.tagName === 'IMG') {
                            forceImageNormalColor(node);
                        }
                        
                        // ค้นหา img elements ใน node ที่เพิ่มเข้ามา
                        if (node.querySelectorAll) {
                            const newImages = node.querySelectorAll('img');
                            newImages.forEach(forceImageNormalColor);
                        }
                    }
                });
                
                // ตรวจสอบ attributes ที่เปลี่ยนแปลง
                if (mutation.type === 'attributes' && 
                    mutation.target.tagName === 'IMG' &&
                    (mutation.attributeName === 'style' || 
                     mutation.attributeName === 'class')) {
                    forceImageNormalColor(mutation.target);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
        
        return observer;
    }
    
    /**
     * Setup event listeners for image events
     */
    function setupImageEventListeners() {
        // ใช้ event delegation เพื่อจัดการกับรูปภาพที่เพิ่มเข้ามาภายหลัง
        document.addEventListener('load', function(e) {
            if (e.target.tagName === 'IMG') {
                forceImageNormalColor(e.target);
            }
        }, true);
        
        document.addEventListener('error', function(e) {
            if (e.target.tagName === 'IMG') {
                forceImageNormalColor(e.target);
            }
        }, true);
        
        // จัดการกับ hover events
        document.addEventListener('mouseenter', function(e) {
            if (e.target.tagName === 'IMG') {
                forceImageNormalColor(e.target);
            }
        }, true);
        
        // จัดการกับ focus events
        document.addEventListener('focus', function(e) {
            if (e.target.tagName === 'IMG') {
                forceImageNormalColor(e.target);
            }
        }, true);
    }
    
    /**
     * Override common CSS filter methods (ปิดการใช้งานเพื่อไม่ให้กระทบกับ interactive elements)
     */
    function overrideFilterMethods() {
        // ปิดการ override เพื่อไม่ให้กระทบกับการทำงานของ Bootstrap และ interactive elements
        /*
        // Override jQuery filter methods if jQuery is available
        if (typeof $ !== 'undefined' && $.fn) {
            const originalCss = $.fn.css;
            $.fn.css = function(prop, value) {
                if (this.is('img') && typeof prop === 'string' && prop.includes('filter')) {
                    return this;
                }
                if (this.is('img') && typeof prop === 'object' && prop.filter) {
                    delete prop.filter;
                }
                return originalCss.apply(this, arguments);
            };
        }

        // Override native style setting
        const originalSetProperty = CSSStyleDeclaration.prototype.setProperty;
        CSSStyleDeclaration.prototype.setProperty = function(property, value, priority) {
            if (this.parentRule && this.parentRule.selectorText &&
                this.parentRule.selectorText.includes('img') &&
                (property.includes('filter') || property === 'opacity')) {
                if (property.includes('filter')) {
                    return;
                }
                if (property === 'opacity' && parseFloat(value) < 1) {
                    value = '1';
                }
            }
            return originalSetProperty.call(this, property, value, priority);
        };
        */
    }
    
    /**
     * Initialize everything
     */
    function init() {
        // Force normal colors immediately
        forceNormalColorsImmediate();
        
        // Setup mutation observer (ปิดการใช้งาน)
        // setupMutationObserver();
        
        // Setup event listeners
        setupImageEventListeners();

        // Override filter methods (ปิดการใช้งาน)
        // overrideFilterMethods();
        
        // Run again after a short delay to catch any missed images (ลดความถี่)
        setTimeout(forceNormalColorsImmediate, 1000);
    }
    
    // เรียกใช้ทันที
    init();
    
    // เรียกใช้เมื่อ DOM พร้อม
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // เรียกใช้เมื่อหน้าเว็บโหลดเสร็จ
    window.addEventListener('load', init);
    
    // Export functions for external use
    window.ForceNormalColors = {
        forceImageNormalColor: forceImageNormalColor,
        forceNormalColorsImmediate: forceNormalColorsImmediate,
        init: init
    };
    
})();

// ปิดการใช้งาน setInterval เพื่อไม่ให้กระทบประสิทธิภาพ
/*
setInterval(function() {
    if (window.ForceNormalColors) {
        window.ForceNormalColors.forceNormalColorsImmediate();
    }
}, 10000);
*/
