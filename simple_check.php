<?php

// เชื่อมต่อฐานข้อมูลโดยตรง
$host = 'localhost';
$dbname = 'child_development_center';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== ตรวจสอบข่าว Project Approach ===\n\n";
    
    // ค้นหาข่าว Project Approach
    $stmt = $pdo->prepare("SELECT * FROM news WHERE title LIKE '%Project Approach%'");
    $stmt->execute();
    $news = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($news) {
        echo "News ID: " . $news['id'] . "\n";
        echo "Title: " . $news['title'] . "\n";
        echo "Main Image: " . ($news['image'] ?? 'null') . "\n";
        
        if ($news['image']) {
            $imagePath = "public/storage/" . $news['image'];
            echo "Image Path: " . $imagePath . "\n";
            echo "Image Exists: " . (file_exists($imagePath) ? 'YES' : 'NO') . "\n";
        }
        
        // ตรวจสอบรูปภาพหลายรูป
        $stmt2 = $pdo->prepare("SELECT * FROM news_images WHERE news_id = ?");
        $stmt2->execute([$news['id']]);
        $images = $stmt2->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Images Count: " . count($images) . "\n";
        
        foreach ($images as $index => $image) {
            echo "\n--- Image " . ($index + 1) . " ---\n";
            echo "ID: " . $image['id'] . "\n";
            echo "Path: " . $image['image_path'] . "\n";
            
            // ตรวจสอบไฟล์
            $filename = basename($image['image_path']);
            $correctPath = "public/storage/news/" . $filename;
            echo "Correct Path: " . $correctPath . "\n";
            echo "File Exists: " . (file_exists($correctPath) ? 'YES' : 'NO') . "\n";
        }
    } else {
        echo "ไม่พบข่าว Project Approach\n";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== เสร็จสิ้น ===\n";
