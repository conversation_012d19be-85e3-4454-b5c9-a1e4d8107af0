/* Image Optimization CSS */
/* ปรับปรุงการแสดงผลรูปภาพให้สมดุลและเห็นรูปหน้าคนชัดเจน */

/* Global Image Styles */
img {
    max-width: 100%;
    height: auto;
    /* ป้องกันการกระพริบ */
    image-rendering: auto;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    transition: none !important;
    /* ให้แน่ใจว่ารูปภาพเป็นสีปกติ */
    filter: none !important;
    opacity: 1 !important;
    -webkit-filter: none !important;
}

/* Card Images */
.card-img-top {
    object-fit: cover;
    object-position: center center;
    transition: transform 0.3s ease;
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

/* Card hover effects removed */

/* Portrait Images (รูปคน) */
.portrait-image,
.staff-avatar,
.rounded-circle {
    object-fit: cover;
    object-position: center 20% !important;
}

/* Landscape Images (รูปกิจกรรม/ข่าว) */
.landscape-image,
.news-card .card-img-top,
.gallery-item img {
    object-fit: cover;
    object-position: center center !important;
}

/* Gallery Specific */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    aspect-ratio: 4/3;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
    transition: none !important; /* ป้องกันการกระพริบ */
    will-change: auto;
}

/* Gallery hover effects removed */

/* News Images */
.news-card {
    /* News card hover effects removed */
}

/* News card hover effects removed */

.news-image {
    height: 250px;
    object-fit: cover;
    object-position: center center;
}

/* Staff Images */
.staff-image {
    aspect-ratio: 1/1;
    object-fit: cover;
    object-position: center 25%;
    border-radius: 50%;
}

/* Admin Panel Images */
.admin-thumbnail {
    height: 200px;
    object-fit: cover;
    object-position: center center;
}

.admin-preview {
    max-height: 500px;
    object-fit: contain;
    object-position: center center;
}

/* Modal Images */
.modal-body img {
    object-fit: contain;
    object-position: center center;
    max-height: 70vh;
    width: auto;
    margin: 0 auto;
    display: block;
    /* ป้องกันการกระพริบใน modal */
    opacity: 1 !important;
    transition: none !important;
    image-rendering: auto;
}

/* Responsive Image Sizes */
@media (max-width: 768px) {
    .news-image {
        height: 200px;
    }
    
    .admin-thumbnail {
        height: 150px;
    }
    
    .gallery-item {
        aspect-ratio: 3/2;
    }
}

@media (max-width: 576px) {
    .news-image {
        height: 180px;
    }
    
    .admin-thumbnail {
        height: 120px;
    }
    
    .gallery-item {
        aspect-ratio: 1/1;
    }
}

/* Image Loading States */
.image-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Image Error States */
.image-error {
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.875rem;
}

.image-error::before {
    content: "📷";
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Lazy Loading */
.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

/* Image Overlay Effects */
.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    padding: 1rem;
    pointer-events: none; /* ป้องกันการบัง element อื่น */
}

.image-container:hover .image-overlay {
    opacity: 1;
}

/* Aspect Ratio Utilities */
.aspect-ratio-1-1 {
    aspect-ratio: 1/1;
}

.aspect-ratio-4-3 {
    aspect-ratio: 4/3;
}

.aspect-ratio-16-9 {
    aspect-ratio: 16/9;
}

.aspect-ratio-3-2 {
    aspect-ratio: 3/2;
}

/* Object Position Utilities */
.object-top {
    object-position: center top !important;
}

.object-center {
    object-position: center center !important;
}

.object-bottom {
    object-position: center bottom !important;
}

.object-face {
    object-position: center 20% !important;
}

/* Image Quality Enhancements */
.crisp-edges {
    image-rendering: crisp-edges;
    image-rendering: -webkit-optimize-contrast;
}

.smooth {
    image-rendering: auto;
    image-rendering: smooth;
}

/* Print Styles */
@media print {
    .card-img-top,
    .gallery-item img,
    .staff-avatar {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card-img-top,
    .gallery-item img,
    .staff-avatar {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Focus States for Accessibility */
.card-img-top:focus,
.gallery-item img:focus,
.staff-avatar:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .image-error {
        background: #343a40;
        color: #adb5bd;
    }

    .image-loading {
        background: linear-gradient(90deg, #495057 25%, #6c757d 50%, #495057 75%);
        background-size: 200% 100%;
    }
}

/* ========== Force Normal Colors for All Images ========== */
/* ให้แน่ใจว่ารูปภาพทั้งหมดแสดงเป็นสีปกติ */
img,
.card-img-top,
.gallery-item img,
.news-image,
.staff-image,
.staff-avatar,
.admin-thumbnail,
.admin-preview,
.portrait-image,
.landscape-image,
.rounded-circle {
    /* ลบ filter ทั้งหมด */
    filter: none !important;
    -webkit-filter: none !important;
    -moz-filter: none !important;
    -ms-filter: none !important;
    -o-filter: none !important;

    /* ให้แน่ใจว่า opacity เป็น 1 */
    opacity: 1 !important;

    /* ลบ effects ที่อาจทำให้สีเปลี่ยน */
    mix-blend-mode: normal !important;
    isolation: auto !important;

    /* ให้แน่ใจว่าการแสดงผลสีถูกต้อง */
    color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
}

/* ป้องกันการใช้ filter ใน hover states */
img:hover,
.card-img-top:hover,
.gallery-item:hover img,
.news-image:hover,
.staff-image:hover,
.staff-avatar:hover {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
}

/* ป้องกันการใช้ filter ใน focus states */
img:focus,
.card-img-top:focus,
.gallery-item img:focus,
.staff-avatar:focus {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
}

/* ป้องกันการใช้ filter ใน active states */
img:active,
.card-img-top:active,
.gallery-item img:active {
    filter: none !important;
    -webkit-filter: none !important;
    opacity: 1 !important;
}

/* Override สำหรับ image error states */
.image-error {
    opacity: 1 !important;
    filter: none !important;
}
