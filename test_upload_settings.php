<?php
echo "=== ทดสอบการตั้งค่าการอัปโหลดหลังจากแก้ไข .htaccess ===\n\n";

// ตรวจสอบการตั้งค่าที่สำคัญ
$settings = [
    'max_file_uploads' => ini_get('max_file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'max_input_time' => ini_get('max_input_time'),
    'memory_limit' => ini_get('memory_limit')
];

echo "การตั้งค่าปัจจุบัน:\n";
foreach ($settings as $setting => $value) {
    echo sprintf("%-20s: %s\n", $setting, $value);
}

echo "\n=== สถานะการอัปโหลดไฟล์ ===\n";

if ($settings['max_file_uploads'] >= 200) {
    echo "✓ max_file_uploads: " . $settings['max_file_uploads'] . " (เพียงพอสำหรับการอัปโหลดไม่จำกัด)\n";
} else {
    echo "⚠ max_file_uploads: " . $settings['max_file_uploads'] . " (ยังจำกัดอยู่)\n";
}

// แปลงขนาดเป็น bytes เพื่อเปรียบเทียบ
function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);
    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    } else {
        return round($size);
    }
}

$postMaxBytes = parseSize($settings['post_max_size']);
$uploadMaxBytes = parseSize($settings['upload_max_filesize']);
$maxFiles = (int)$settings['max_file_uploads'];

$totalCapacity = $uploadMaxBytes * $maxFiles;

echo "\nความจุการอัปโหลด:\n";
echo "- ขนาดไฟล์เดี่ยวสูงสุด: " . $settings['upload_max_filesize'] . "\n";
echo "- จำนวนไฟล์สูงสุด: " . $maxFiles . " ไฟล์\n";
echo "- ความจุรวมทั้งหมด: " . round($totalCapacity / 1024 / 1024) . " MB\n";
echo "- ขีดจำกัด POST: " . $settings['post_max_size'] . "\n";

if ($postMaxBytes >= $totalCapacity) {
    echo "✓ การตั้งค่าเหมาะสมสำหรับการอัปโหลดไฟล์จำนวนมาก\n";
} else {
    echo "⚠ post_max_size อาจไม่เพียงพอ\n";
}

echo "\n=== คำแนะนำการใช้งาน ===\n";
echo "1. สามารถอัปโหลดได้สูงสุด " . $maxFiles . " ไฟล์ต่อครั้ง\n";
echo "2. แต่ละไฟล์ขนาดไม่เกิน " . $settings['upload_max_filesize'] . "\n";
echo "3. ขนาดรวมทั้งหมดไม่เกิน " . $settings['post_max_size'] . "\n";
echo "4. เวลาประมวลผลสูงสุด " . $settings['max_execution_time'] . " วินาที\n";

echo "\n=== การทดสอบ ===\n";
echo "ลองอัปโหลดรูปภาพจำนวนมากผ่านระบบเพื่อทดสอบ\n";
echo "หากยังมีปัญหา ให้ตรวจสอบ:\n";
echo "1. การตั้งค่าใน php.ini\n";
echo "2. การ restart Apache\n";
echo "3. การตั้งค่าของ Web Server\n";
