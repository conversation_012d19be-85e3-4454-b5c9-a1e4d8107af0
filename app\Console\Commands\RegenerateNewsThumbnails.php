<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NewsImage;
use Illuminate\Support\Facades\File;

class RegenerateNewsThumbnails extends Command
{
    protected $signature = 'news:regenerate-thumbnails';
    protected $description = 'Regenerate missing thumbnails for news images';

    public function handle()
    {
        $this->info('Starting thumbnail regeneration for news images...');

        $newsImages = NewsImage::all();
        $regenerated = 0;
        $errors = 0;

        foreach ($newsImages as $image) {
            $this->info("Processing image ID: {$image->id}");

            if (!$image->thumbnail_path) {
                $this->warn("  - No thumbnail path set, skipping");
                continue;
            }

            $thumbnailPath = public_path('storage/' . str_replace('\\', '/', $image->thumbnail_path));
            
            if (file_exists($thumbnailPath)) {
                $this->info("  - Thumbnail already exists, skipping");
                continue;
            }

            // ลองสร้าง thumbnail ใหม่โดยคัดลอกจากรูปต้นฉบับ
            if ($this->createThumbnailByCopy($image)) {
                $regenerated++;
                $this->info("  - ✅ Thumbnail created successfully");
            } else {
                $errors++;
                $this->error("  - ❌ Failed to create thumbnail");
            }
        }

        $this->info("\n=== Summary ===");
        $this->info("Total images processed: " . $newsImages->count());
        $this->info("Thumbnails regenerated: {$regenerated}");
        $this->info("Errors: {$errors}");

        if ($regenerated > 0) {
            $this->info("\n✅ Thumbnail regeneration completed successfully!");
        } else {
            $this->warn("\n⚠️  No thumbnails were regenerated.");
        }

        return 0;
    }

    /**
     * สร้าง thumbnail โดยคัดลอกจากรูปต้นฉบับ
     */
    private function createThumbnailByCopy($image)
    {
        if (!$image->image_path || !$image->thumbnail_path) {
            return false;
        }

        $originalPath = public_path('storage/' . str_replace('\\', '/', $image->image_path));
        $thumbnailPath = public_path('storage/' . str_replace('\\', '/', $image->thumbnail_path));

        // ตรวจสอบว่ารูปต้นฉบับมีอยู่หรือไม่
        if (!file_exists($originalPath)) {
            $this->error("    Original file not found: {$originalPath}");
            return false;
        }

        // สร้างโฟลเดอร์ thumbnail หากยังไม่มี
        $thumbnailDir = dirname($thumbnailPath);
        if (!file_exists($thumbnailDir)) {
            if (!mkdir($thumbnailDir, 0755, true)) {
                $this->error("    Failed to create directory: {$thumbnailDir}");
                return false;
            }
        }

        // คัดลอกไฟล์
        if (copy($originalPath, $thumbnailPath)) {
            $this->info("    Copied: {$originalPath} -> {$thumbnailPath}");
            return true;
        } else {
            $this->error("    Failed to copy file");
            return false;
        }
    }
}
