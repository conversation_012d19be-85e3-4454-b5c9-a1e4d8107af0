<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\News;
use App\Models\NewsImage;
use Illuminate\Support\Facades\Storage;

class NewsSeeder extends Seeder
{
    public function run()
    {
        // ข้อมูลข่าวสารตัวอย่าง
        $newsData = [
            [
                'title' => 'กิจกรรมวันเด็กแห่งชาติ ประจำปี 2568',
                'content' => 'ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า จัดกิจกรรมวันเด็กแห่งชาติ ประจำปี 2568 ขึ้นในวันที่ 11 มกราคม 2568 โดยมีกิจกรรมหลากหลายสำหรับเด็กๆ อาทิ การแสดงดนตรี การเล่นเกม การแจกของขวัญ และการแสดงละครเวที

เด็กๆ ได้รับความสนุกสนานและมีความสุขกับกิจกรรมต่างๆ ที่จัดขึ้น ผู้ปกครองและชุมชนให้ความร่วมมือเป็นอย่างดี ทำให้งานประสบความสำเร็จอย่างยิ่ง

กิจกรรมนี้มีวัตถุประสงค์เพื่อส่งเสริมพัฒนาการของเด็ก สร้างความสุขและความทรงจำที่ดีให้กับเด็กๆ รวมถึงเสริมสร้างความสัมพันธ์ที่ดีระหว่างครอบครัวและชุมชน',
                'is_published' => true,
                'images' => [
                    'sample_news_1_1.jpg',
                    'sample_news_1_2.jpg'
                ]
            ],
            [
                'title' => 'โครงการส่งเสริมการเรียนรู้ผ่านการเล่น',
                'content' => 'ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า ได้เริ่มดำเนินโครงการส่งเสริมการเรียนรู้ผ่านการเล่น เพื่อพัฒนาทักษะการเรียนรู้ของเด็กปฐมวัยอย่างเป็นระบบ

โครงการนี้เน้นการใช้เกมและกิจกรรมสนุกสนานเป็นสื่อในการเรียนรู้ ช่วยให้เด็กๆ ได้พัฒนาทั้งด้านร่างกาย จิตใจ สังคม และสติปัญญาไปพร้อมๆ กัน

ครูผู้สอนได้รับการอบรมเพิ่มเติมเกี่ยวกับวิธีการสอนแบบใหม่ และมีการจัดหาอุปกรณ์การเรียนการสอนที่ทันสมัยมาใช้ในการจัดกิจกรรม

ผลลัพธ์ที่คาดหวังคือ เด็กๆ จะมีพัฒนาการที่ดีขึ้นในทุกด้าน มีความสุขในการเรียนรู้ และเตรียมความพร้อมสำหรับการเข้าสู่ระบบการศึกษาในระดับที่สูงขึ้น',
                'is_published' => true,
                'images' => [
                    'sample_news_2_1.jpg',
                    'sample_news_2_2.jpg',
                    'sample_news_2_3.jpg'
                ]
            ],
            [
                'title' => 'การอบรมผู้ปกครองเรื่องการดูแลเด็กปฐมวัย',
                'content' => 'ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า จัดการอบรมผู้ปกครองเรื่อง "การดูแลและส่งเสริมพัฒนาการเด็กปฐมวัย" เมื่อวันที่ 15 ธันวาคม 2567

การอบรมครั้งนี้มีวิทยากรผู้เชี่ยวชาญด้านการพัฒนาเด็กมาให้ความรู้เกี่ยวกับ:
- การดูแลสุขภาพเด็กปฐมวัย
- การส่งเสริมพัฒนาการด้านต่างๆ
- การจัดกิจกรรมเสริมการเรียนรู้ที่บ้าน
- การแก้ไขปัญหาพฤติกรรมเด็ก

ผู้ปกครองให้ความสนใจเข้าร่วมเป็นจำนวนมาก และได้รับความรู้ที่เป็นประโยชน์ในการดูแลลูกหลานที่บ้าน ทำให้การพัฒนาเด็กเป็นไปอย่างต่อเนื่องทั้งที่บ้านและที่ศูนย์',
                'is_published' => true,
                'images' => [
                    'sample_news_3_1.jpg'
                ]
            ],
            [
                'title' => 'ประกาศรับสมัครเด็กใหม่ ปีการศึกษา 2568',
                'content' => 'ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า ประกาศรับสมัครเด็กใหม่ ปีการศึกษา 2568 สำหรับเด็กอายุ 2-5 ปี

รายละเอียดการรับสมัคร:
📅 วันที่รับสมัคร: 1-31 มีนาคม 2568
🕐 เวลา: 08:00-16:00 น. (วันจันทร์-ศุกร์)
📍 สถานที่: ศูนย์พัฒนาเด็กเล็กตำบลบุ่งคล้า

เอกสารที่ต้องใช้:
- สำเนาทะเบียนบ้านเด็ก
- สำเนาบัตรประชาชนผู้ปกครอง
- ใบรับรองแพทย์
- รูปถ่าย 1 นิ้ว จำนวน 2 รูป

คุณสมบัติ:
- เด็กอายุ 2-5 ปี (นับถึงวันที่ 1 มิถุนายน 2568)
- มีภูมิลำเนาในตำบลบุ่งคล้า หรือ ผู้ปกครองทำงานในพื้นที่

สอบถามรายละเอียดเพิ่มเติมได้ที่ โทร. 034-123-456',
                'is_published' => true,
                'images' => [
                    'sample_news_4_1.jpg',
                    'sample_news_4_2.jpg'
                ]
            ]
        ];

        foreach ($newsData as $index => $data) {
            // สร้างข่าว
            $news = News::create([
                'title' => $data['title'],
                'content' => $data['content'],
                'is_published' => $data['is_published'],
                'created_at' => now()->subDays(rand(1, 30)),
                'updated_at' => now()->subDays(rand(1, 30))
            ]);

            // สร้างรูปภาพตัวอย่าง
            foreach ($data['images'] as $imageIndex => $imageName) {
                $this->createSampleImage($news, $imageName, $imageIndex);
            }
        }
    }

    private function createSampleImage($news, $imageName, $index)
    {
        // สร้างไฟล์ข้อความแทนรูปภาพ (เพื่อทดสอบ)
        $imagePath = 'news/' . $imageName;
        $fullImagePath = storage_path('app/public/' . $imagePath);

        // สร้างโฟลเดอร์หากไม่มี
        if (!file_exists(dirname($fullImagePath))) {
            mkdir(dirname($fullImagePath), 0755, true);
        }

        // สร้างไฟล์ placeholder (copy จากรูปที่มีอยู่แล้ว)
        $sourceImages = [
            storage_path('app/public/517405122_2237056203375265_5857228261068252325_n.jpg'),
            storage_path('app/public/518121656_2237055750041977_1526893695761893889_n.jpg'),
            storage_path('app/public/518249369_2237055203375365_4639076436228161912_n.jpg'),
            storage_path('app/public/518315043_2237055156708703_575782899309704948_n.jpg')
        ];

        // เลือกรูปต้นฉบับแบบสุ่ม
        $sourceImage = $sourceImages[array_rand($sourceImages)];

        if (file_exists($sourceImage)) {
            copy($sourceImage, $fullImagePath);
        } else {
            // หากไม่มีรูปต้นฉบับ ให้สร้างไฟล์ว่าง
            file_put_contents($fullImagePath, 'Sample image placeholder');
        }

        // สร้าง thumbnail
        $thumbnailPath = 'news/thumbnails/' . $imageName;
        $fullThumbnailPath = storage_path('app/public/' . $thumbnailPath);

        // สร้างโฟลเดอร์ thumbnail หากไม่มี
        if (!file_exists(dirname($fullThumbnailPath))) {
            mkdir(dirname($fullThumbnailPath), 0755, true);
        }

        // copy รูปเดียวกันเป็น thumbnail
        if (file_exists($fullImagePath) && filesize($fullImagePath) > 100) {
            copy($fullImagePath, $fullThumbnailPath);
        } else {
            file_put_contents($fullThumbnailPath, 'Sample thumbnail placeholder');
        }

        // บันทึกข้อมูลในฐานข้อมูล
        NewsImage::create([
            'news_id' => $news->id,
            'image_path' => $imagePath,
            'thumbnail_path' => $thumbnailPath,
            'sort_order' => $index,
            'is_featured' => $index === 0 // รูปแรกเป็น featured
        ]);
    }
}
