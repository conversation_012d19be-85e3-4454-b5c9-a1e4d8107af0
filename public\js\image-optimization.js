/**
 * Image Optimization JavaScript
 * ปรับปรุงการแสดงผลรูปภาพให้สมดุลและเห็นรูปหน้าคนชัดเจน
 */

document.addEventListener('DOMContentLoaded', function() {

    // Initialize image optimization
    initImageOptimization();

    // Initialize lazy loading
    initLazyLoading();

    // Initialize image error handling
    initImageErrorHandling();

    // Initialize image aspect ratio detection
    initAspectRatioDetection();

    // Force normal colors for all images
    forceNormalImageColors();

});

/**
 * Initialize image optimization
 */
function initImageOptimization() {
    const images = document.querySelectorAll('img:not(.modal-body img)'); // ยกเว้นรูปใน modal

    images.forEach(img => {
        // ป้องกันการกระพริบโดยการตั้งค่า style เริ่มต้น
        img.style.transition = 'none';
        img.style.imageRendering = 'auto';

        // Add loading class
        img.classList.add('image-loading');

        // Handle image load
        img.addEventListener('load', function() {
            this.classList.remove('image-loading');
            this.classList.add('loaded');

            // ป้องกันการกระพริบ
            this.style.opacity = '1';
            this.style.transition = 'none';

            // Auto-detect if image contains people and adjust object-position
            detectAndAdjustImagePosition(this);
        });

        // Handle image error
        img.addEventListener('error', function() {
            this.classList.remove('image-loading');
            this.classList.add('image-error');
            this.style.opacity = '1'; // เปลี่ยนจาก 0.5 เป็น 1 เพื่อให้เป็นสีปกติ
            this.style.filter = 'none'; // ให้แน่ใจว่าไม่มี filter
            handleImageError(this);
        });
    });
}

/**
 * Initialize lazy loading for images
 */
function initLazyLoading() {
    const lazyImages = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loading');
                    imageObserver.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });
        
        lazyImages.forEach(img => {
            img.classList.add('lazy');
            imageObserver.observe(img);
        });
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        lazyImages.forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
        });
    }
}

/**
 * Initialize image error handling
 */
function initImageErrorHandling() {
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
        img.addEventListener('error', function() {
            handleImageError(this);
        });
    });
}

/**
 * Handle image loading errors
 */
function handleImageError(img) {
    // Create placeholder based on image context
    const placeholder = createImagePlaceholder(img);
    
    // Replace image with placeholder
    if (placeholder) {
        img.parentNode.replaceChild(placeholder, img);
    }
}

/**
 * Create appropriate placeholder for failed images
 */
function createImagePlaceholder(img) {
    const placeholder = document.createElement('div');
    const width = img.offsetWidth || img.getAttribute('width') || 300;
    const height = img.offsetHeight || img.getAttribute('height') || 200;
    
    placeholder.className = 'image-error d-flex align-items-center justify-content-center';
    placeholder.style.width = width + 'px';
    placeholder.style.height = height + 'px';
    placeholder.style.backgroundColor = '#f8f9fa';
    placeholder.style.border = '1px solid #dee2e6';
    placeholder.style.borderRadius = img.style.borderRadius || '0';
    
    // Determine placeholder content based on image context
    let content = '';
    if (img.classList.contains('staff-avatar') || img.alt.includes('บุคลากร')) {
        content = '<i class="fas fa-user fa-3x text-muted"></i>';
    } else if (img.classList.contains('news-image') || img.alt.includes('ข่าว')) {
        content = '<i class="fas fa-newspaper fa-3x text-muted"></i>';
    } else if (img.classList.contains('gallery-image') || img.alt.includes('กิจกรรม')) {
        content = '<i class="fas fa-images fa-3x text-muted"></i>';
    } else {
        content = '<i class="fas fa-image fa-3x text-muted"></i>';
    }
    
    placeholder.innerHTML = content;
    return placeholder;
}

/**
 * Detect and adjust image position based on content
 */
function detectAndAdjustImagePosition(img) {
    // Check if image is likely to contain people
    const isPeopleImage = isProbablyPeopleImage(img);
    
    if (isPeopleImage) {
        // Adjust object-position for better face visibility
        img.style.objectPosition = 'center 20%';
        img.classList.add('object-face');
    } else {
        // Use center positioning for landscape/activity images
        img.style.objectPosition = 'center center';
        img.classList.add('object-center');
    }
}

/**
 * Determine if image likely contains people
 */
function isProbablyPeopleImage(img) {
    const alt = img.alt.toLowerCase();
    const src = img.src.toLowerCase();
    const className = img.className.toLowerCase();
    
    // Keywords that suggest people in images
    const peopleKeywords = [
        'บุคลากร', 'ครู', 'อาจารย์', 'ผู้อำนวยการ', 'staff', 'teacher',
        'คน', 'เด็ก', 'นักเรียน', 'student', 'child', 'people',
        'portrait', 'face', 'หน้า', 'ใบหน้า'
    ];
    
    // Check alt text, src, and class names
    const hasKeyword = peopleKeywords.some(keyword => 
        alt.includes(keyword) || src.includes(keyword) || className.includes(keyword)
    );
    
    // Check if it's a staff/profile image based on class names
    const isProfileImage = className.includes('staff') || 
                           className.includes('avatar') || 
                           className.includes('profile') ||
                           className.includes('rounded-circle');
    
    return hasKeyword || isProfileImage;
}

/**
 * Initialize aspect ratio detection and adjustment
 */
function initAspectRatioDetection() {
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
        img.addEventListener('load', function() {
            adjustImageAspectRatio(this);
        });
    });
}

/**
 * Adjust image aspect ratio based on content
 */
function adjustImageAspectRatio(img) {
    const aspectRatio = img.naturalWidth / img.naturalHeight;
    
    // Add appropriate aspect ratio class
    if (Math.abs(aspectRatio - 1) < 0.1) {
        // Square image
        img.classList.add('aspect-ratio-1-1');
    } else if (aspectRatio > 1.5) {
        // Wide image
        img.classList.add('aspect-ratio-16-9');
    } else if (aspectRatio > 1.2) {
        // Landscape image
        img.classList.add('aspect-ratio-4-3');
    } else {
        // Portrait image
        img.classList.add('aspect-ratio-3-2');
    }
}

/**
 * Utility function to preload images
 */
function preloadImage(src) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = src;
    });
}

/**
 * Utility function to get optimal image size
 */
function getOptimalImageSize(container) {
    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;
    const devicePixelRatio = window.devicePixelRatio || 1;
    
    return {
        width: Math.ceil(containerWidth * devicePixelRatio),
        height: Math.ceil(containerHeight * devicePixelRatio)
    };
}

/**
 * Add smooth loading animation
 */
function addLoadingAnimation(img) {
    img.style.opacity = '0';
    img.style.transition = 'opacity 0.3s ease';
    
    img.addEventListener('load', function() {
        this.style.opacity = '1';
    });
}

/**
 * Initialize image zoom functionality for gallery
 */
function initImageZoom() {
    const galleryImages = document.querySelectorAll('.gallery-item img');
    
    galleryImages.forEach(img => {
        img.addEventListener('click', function() {
            showImageModal(this.src, this.alt);
        });
    });
}

/**
 * Show image in modal
 */
function showImageModal(src, alt) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('imageModal');
    if (!modal) {
        modal = createImageModal();
        document.body.appendChild(modal);
    }

    // Update modal content
    const modalImg = modal.querySelector('.modal-body img');
    const modalTitle = modal.querySelector('.modal-title');

    // ป้องกันการกระพริบโดยการตั้งค่า style ก่อน
    modalImg.style.opacity = '1';
    modalImg.style.transition = 'none';
    modalImg.style.imageRendering = 'auto';
    modalImg.src = src;
    modalImg.alt = alt;
    modalTitle.textContent = alt;

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

/**
 * Create image modal
 */
function createImageModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'imageModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="" alt="" class="img-fluid" style="opacity: 1 !important; transition: none !important; max-height: 70vh; object-fit: contain; image-rendering: auto;">
                </div>
            </div>
        </div>
    `;

    // เพิ่ม event listener เพื่อป้องกันการกระพริบ
    modal.addEventListener('shown.bs.modal', function() {
        const img = this.querySelector('.modal-body img');
        if (img) {
            img.style.opacity = '1';
            img.style.transition = 'none';
        }
    });

    return modal;
}

/**
 * Force normal colors for all images
 * ให้แน่ใจว่ารูปภาพทั้งหมดแสดงเป็นสีปกติ
 */
function forceNormalImageColors() {
    const images = document.querySelectorAll('img');

    images.forEach(img => {
        // ลบ filter ทั้งหมด
        img.style.filter = 'none';
        img.style.webkitFilter = 'none';

        // ให้แน่ใจว่า opacity เป็น 1 (ยกเว้นกรณีที่กำลังโหลด)
        if (!img.classList.contains('image-loading') && !img.classList.contains('lazy')) {
            img.style.opacity = '1';
        }

        // ลบ effects ที่อาจทำให้สีเปลี่ยน
        img.style.mixBlendMode = 'normal';
        img.style.isolation = 'auto';

        // ให้แน่ใจว่าการแสดงผลสีถูกต้อง
        img.style.colorAdjust = 'exact';
        img.style.webkitPrintColorAdjust = 'exact';
        img.style.printColorAdjust = 'exact';

        // เพิ่ม event listener เพื่อป้องกันการเปลี่ยนสีในอนาคต
        img.addEventListener('load', function() {
            this.style.filter = 'none';
            this.style.webkitFilter = 'none';
            this.style.opacity = '1';
        });

        // ป้องกันการเปลี่ยนสีเมื่อ hover
        img.addEventListener('mouseenter', function() {
            this.style.filter = 'none';
            this.style.webkitFilter = 'none';
            this.style.opacity = '1';
        });

        // ป้องกันการเปลี่ยนสีเมื่อ focus
        img.addEventListener('focus', function() {
            this.style.filter = 'none';
            this.style.webkitFilter = 'none';
            this.style.opacity = '1';
        });
    });

    // ตรวจสอบและแก้ไขรูปภาพใหม่ที่เพิ่มเข้ามาภายหลัง
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                    const newImages = node.querySelectorAll ? node.querySelectorAll('img') : [];
                    if (node.tagName === 'IMG') {
                        forceImageNormalColor(node);
                    }
                    newImages.forEach(forceImageNormalColor);
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * Force normal color for a single image
 */
function forceImageNormalColor(img) {
    img.style.filter = 'none';
    img.style.webkitFilter = 'none';
    img.style.opacity = '1';
    img.style.mixBlendMode = 'normal';
    img.style.isolation = 'auto';
    img.style.colorAdjust = 'exact';
    img.style.webkitPrintColorAdjust = 'exact';
    img.style.printColorAdjust = 'exact';
}

// Export functions for external use
window.ImageOptimization = {
    preloadImage,
    getOptimalImageSize,
    addLoadingAnimation,
    showImageModal,
    forceNormalImageColors,
    forceImageNormalColor
};
