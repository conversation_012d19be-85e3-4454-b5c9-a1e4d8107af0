@extends('layouts.admin')

@section('title', 'เพิ่มข่าวสาร - ระบบจัดการ')
@section('page-title', 'เพิ่มข่าวสาร')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>เพิ่มข่าวสารใหม่
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.news.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">หัวข้อข่าว <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control @error('title') is-invalid @enderror" 
                               id="title" 
                               name="title" 
                               value="{{ old('title') }}" 
                               required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">เนื้อหา <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('content') is-invalid @enderror" 
                                  id="content" 
                                  name="content" 
                                  rows="8" 
                                  required>{{ old('content') }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    




                    <!-- รูปภาพเพิ่มเติม (หลายรูป) -->
                    <div class="mb-3">
                        <label for="images" class="form-label">รูปภาพเพิ่มเติม</label>
                        <input type="file"
                               class="form-control @error('images.*') is-invalid @enderror"
                               id="images"
                               name="images[]"
                               accept="image/*"
                               multiple
                               onchange="previewMultipleImages(this)">
                        @error('images.*')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            สามารถเลือกได้หลายรูป (สูงสุด 10 รูป) | รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 5MB ต่อรูป)
                        </div>

                        <!-- Preview รูปภาพหลายรูป -->
                        <div id="multipleImagesPreview" class="mt-3" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปภาพที่เลือก:</label>
                            <div id="previewContainer" class="row g-2"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="is_published" 
                                   name="is_published" 
                                   value="1" 
                                   {{ old('is_published', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_published">
                                เผยแพร่ทันที
                            </label>
                        </div>
                        <div class="form-text">หากไม่เลือก ข่าวจะถูกบันทึกเป็นร่าง</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>กลับ
                        </a>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>บันทึก
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>


// ฟังก์ชันสำหรับแสดงตัวอย่างรูปภาพหลายรูป
function previewMultipleImages(input) {
    const previewContainer = document.getElementById('multipleImagesPreview');
    const imageContainer = document.getElementById('previewContainer');

    // ล้างรูปภาพเก่า
    imageContainer.innerHTML = '';

    if (input.files && input.files.length > 0) {
        // ตรวจสอบจำนวนรูปภาพ
        if (input.files.length > 10) {
            alert('สามารถเลือกได้สูงสุด 10 รูปเท่านั้น');
            input.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        previewContainer.style.display = 'block';

        // แสดงตัวอย่างแต่ละรูป
        Array.from(input.files).forEach((file, index) => {
            // ตรวจสอบขนาดไฟล์
            if (file.size > 5 * 1024 * 1024) { // 5MB
                alert(`รูปภาพ "${file.name}" มีขนาดใหญ่เกิน 5MB`);
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const col = document.createElement('div');
                col.className = 'col-md-3 col-sm-4 col-6';

                col.innerHTML = `
                    <div class="card">
                        <img src="${e.target.result}"
                             class="card-img-top"
                             style="height: 150px; object-fit: cover;"
                             alt="Preview ${index + 1}">
                        <div class="card-body p-2">
                            <small class="text-muted">รูปที่ ${index + 1}</small>
                            <br>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                        </div>
                    </div>
                `;

                imageContainer.appendChild(col);
            };

            reader.readAsDataURL(file);
        });
    } else {
        previewContainer.style.display = 'none';
    }
}

// ฟังก์ชันสำหรับแปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// เพิ่ม event listener สำหรับตรวจสอบการเปลี่ยนแปลงไฟล์
document.addEventListener('DOMContentLoaded', function() {
    const imagesInput = document.getElementById('images');

    // ตรวจสอบเมื่อมีการเลือกไฟล์
    imagesInput.addEventListener('change', function() {
        const files = this.files;
        let totalSize = 0;

        // คำนวณขนาดรวม
        Array.from(files).forEach(file => {
            totalSize += file.size;
        });

        // แสดงข้อมูลสรุป
        if (files.length > 0) {
            console.log(`เลือกรูปภาพ ${files.length} รูป, ขนาดรวม: ${formatFileSize(totalSize)}`);
        }
    });
});
</script>
@endsection
